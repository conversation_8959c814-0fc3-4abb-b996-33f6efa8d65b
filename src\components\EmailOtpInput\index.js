"use client";
import { useRef, useState } from "react";
// import styles from "./styles.module.css";
const EmailOtpInput = ({ onOtpChange, error }) => {
  const [otp, setOtp] = useState(new Array(4).fill(""));
  const inputsRef = useRef([]);

  const handleChange = (element, index) => {
    if (isNaN(element.value)) return;

    let newOtp = [...otp];
    newOtp[index] = element.value;
    setOtp(newOtp);

    if (element.value !== "" && index < 5) {
      inputsRef?.current[index + 1]?.focus();
    }
    onOtpChange(newOtp.join(""));
  };

  const handleKeyDown = (event, index) => {
    if (event.key === "Backspace") {
      if (otp[index] === "" && index > 0) {
        inputsRef?.current[index - 1]?.focus();
      }
    }
  };

  const handlePaste = (event) => {
    const pastedData = event.clipboardData.getData("text").slice(0, 6).split("");

    setOtp(pastedData);
    onOtpChange(pastedData.join(""));
    pastedData.forEach((value, index) => {
      if (inputsRef?.current[index]) {
        inputsRef.current[index].value = value;
        if (index < 3) {
          inputsRef?.current[index + 1]?.focus();
        }
      }
    });
  };

  return (
    <div className="flex justify-between max-[470px]:gap-6">
      {otp.map((value, index) => (
        <input
          key={index}
          type="text"
          maxLength="1"
          value={value}
          onChange={(e) => handleChange(e.target, index)}
          onKeyDown={(e) => handleKeyDown(e, index)}
          onPaste={handlePaste}
          ref={(el) => (inputsRef.current[index] = el)}
          className={`w-[90px] h-[76.3px] box-border border-[2.27px] border-gray-400 rounded-[1.5rem] p-6 text-[1.7rem] mb-8 focus:outline-none placeholder:text-[1.7rem] placeholder:text-gray-400 max-[470px]:w-[50px] max-[470px]:h-[50px] max-[470px]:p-4 max-[470px]:text-[1.3rem] max-[470px]:mb-6 max-[470px]:text-center max-[470px]:rounded-[1rem] ${error ? "border-red-500 bg-red-50" : ""}`}
        />
      ))}
    </div>
  );
};

export default EmailOtpInput;
