// import styles from "./styles.module.css";

export const UserImgShimmer = () => {
  return (
    <div className="flex items-center gap-[10px]">
      <div className="w-[60px] h-[60px] rounded-full animate-pulse bg-gradient-to-r from-gray-300 via-gray-100 to-gray-300 bg-[length:1200px_100%] animate-[shimmer_2.2s_linear_infinite]"></div>
      <div className="w-[160px] h-[40px] rounded-[20px] animate-pulse bg-gradient-to-r from-gray-300 via-gray-100 to-gray-300 bg-[length:1200px_100%] animate-[shimmer_2.2s_linear_infinite]"></div>
    </div>
  );
};

export const HomeTilesShimmer = () => {
  return (
    <>
      <div className="w-[290px] h-[200px] skew-x-[10deg] relative rounded-[14px] overflow-hidden flex-shrink-0 box-border animate-pulse bg-gradient-to-r from-gray-300 via-gray-100 to-gray-300 bg-[length:1200px_100%] animate-[shimmer_2.2s_linear_infinite] max-[500px]:landscape:w-[155px] max-[500px]:landscape:h-[105px]" />
      <div className="w-[290px] h-[200px] skew-x-[10deg] relative rounded-[14px] overflow-hidden flex-shrink-0 box-border animate-pulse bg-gradient-to-r from-gray-300 via-gray-100 to-gray-300 bg-[length:1200px_100%] animate-[shimmer_2.2s_linear_infinite] max-[500px]:landscape:w-[155px] max-[500px]:landscape:h-[105px]" />
      <div className="w-[290px] h-[200px] skew-x-[10deg] relative rounded-[14px] overflow-hidden flex-shrink-0 box-border animate-pulse bg-gradient-to-r from-gray-300 via-gray-100 to-gray-300 bg-[length:1200px_100%] animate-[shimmer_2.2s_linear_infinite] max-[500px]:landscape:w-[155px] max-[500px]:landscape:h-[105px]" />
      <div className="w-[290px] h-[200px] skew-x-[10deg] relative rounded-[14px] overflow-hidden flex-shrink-0 box-border animate-pulse bg-gradient-to-r from-gray-300 via-gray-100 to-gray-300 bg-[length:1200px_100%] animate-[shimmer_2.2s_linear_infinite] max-[500px]:landscape:w-[155px] max-[500px]:landscape:h-[105px]" />
      <div className="w-[290px] h-[200px] skew-x-[10deg] relative rounded-[14px] overflow-hidden flex-shrink-0 box-border animate-pulse bg-gradient-to-r from-gray-300 via-gray-100 to-gray-300 bg-[length:1200px_100%] animate-[shimmer_2.2s_linear_infinite] max-[500px]:landscape:w-[155px] max-[500px]:landscape:h-[105px]" />
    </>
  );
};
