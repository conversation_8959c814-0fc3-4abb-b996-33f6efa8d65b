
import FormCtaButton from "@/components/FormCtaButton";

const PricingCard = ({
  planName,
  price,
  originalPrice,
  offerText,
  description,
  onActivate,
  isBestValue = false,
  className = "",
  buttonText = "Activate",
  buttonClassName = ""
}) => {
  if (isBestValue) {
    return (
      <div className={`relative max-[600px]:flex max-[600px]:justify-center max-[600px]:order-1 ${className}`}>
        <div className="bg-white absolute left-1/2 transform -translate-x-1/2 w-[190px] py-[0.7rem] px-[0.8rem] -top-[15px] rounded-[20px] text-label text-center transition-all duration-300 ease-out hover:-top-[50px] hover:w-[150px] hover:rounded-[40px] lg:w-[150px] lg:py-[0.6rem] lg:px-[0.8rem] lg:-top-[15px] lg:rounded-[20px] lg:hover:w-[110px] md:w-[120px] md:py-[0.6rem] md:px-[0.8rem] md:-top-[15px] md:hover:-top-[35px] md:hover:w-[90px] max-[600px]:w-[150px] max-[600px]:-top-[10px] max-[600px]:py-[0.5rem] max-[600px]:px-[0.7rem] max-[600px]:pb-4 max-[600px]:rounded-[20px] max-[600px]:hover:w-[120px] max-[600px]:hover:left-1/2 max-[600px]:hover:-top-[35px] max-[600px]:hover:rounded-[40px] max-[600px]:hover:py-[0.5rem] max-[600px]:hover:px-[0.6rem]">
          Best Value
        </div>
        <div className="w-[380px] h-[380px] bg-[url('/images/acquisition/heroCardBg.svg')] bg-cover bg-center relative z-10 transition-transform duration-300 ease hover:scale-[1.02] hover:cursor-pointer lg:w-[320px] lg:h-[320px] md:w-[256px] md:h-[256px] max-[600px]:w-[300px] max-[600px]:h-[300px]">
          <div className="flex flex-col items-center justify-center h-full my-0 mx-8 md:my-0 md:mx-6">
            <p className="heading-tertiary text-white m-0 mt-4">
              {planName}
            </p>
            <p className="flex items-center gap-[10px] lg:gap-[5px] md:gap-[5px]">
              {originalPrice && originalPrice !== "---" && (
                <>
                  <span className="text-white/70 text-lg line-through lg:text-base md:text-sm">
                    {originalPrice}
                  </span>
                  {offerText && offerText.trim().length > 0 && (
                    <span className="text-base bg-secondary-500 py-1 px-2 text-white rounded-lg lg:py-1 lg:px-2 md:text-sm md:py-1 md:px-1">
                      {offerText}
                    </span>
                  )}
                </>
              )}
            </p>
            <p className="text-white heading-tertiary m-0">{price}</p>
            <p className="text-center text-body text-white p-0">
              {description}
            </p>
            <FormCtaButton
              text={buttonText}
              onClick={onActivate}
              customStyles={`w-full rounded-4 lg:rounded-[0.8rem] md:rounded-[0.5rem] max-[600px]:rounded-[0.8rem] ${buttonClassName}`}
            />
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`card-pricing self-end max-[600px]:self-center max-[600px]:order-2 ${className}`}>
      <p className="heading-tertiary m-0">
        {planName}
      </p>
      <p className="flex items-center gap-2">
        {originalPrice && originalPrice !== "---" && (
          <>
            <span className="text-gray-500 text-lg line-through md:text-sm">
              {originalPrice}
            </span>
            {offerText && offerText.trim().length > 0 && (
              <span className="bg-secondary-500 text-white py-1 px-2 text-sm rounded-lg">
                {offerText}
              </span>
            )}
          </>
        )}
      </p>
      <p className="heading-tertiary m-0">{price}</p>
      <p className="text-body-sm text-center">
        {description}
      </p>
      <FormCtaButton
        text={buttonText}
        onClick={onActivate}
        customStyles={`btn-secondary w-full ${buttonClassName}`}
      />
    </div>
  );
};

export default PricingCard;
