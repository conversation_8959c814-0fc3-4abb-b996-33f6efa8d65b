import { useTranslations } from "next-intl";
import Head from "next/head";
import Image from "next/image";
import Mascots from "../../../public/images/contactUs/contactUsMascots.webp";

const ContactUs = () => {
  const t = useTranslations("ContactUs");

  return (
    <>
      <Head>
        <title>{t("title")}</title>
        <meta name="description" content={t("metaDescription")} />
      </Head>
      <div className="max-w-[1728px] mx-auto">
        <header className="max-w-full bg-[url('/images/contactUs/contactUsBanner.webp')] bg-cover bg-center bg-no-repeat flex object-cover flex-col md:flex-row">
          <div className="flex w-[95%] md:w-1/2 justify-center flex-col mx-auto md:px-12 md:my-auto">
            <h2 className="text-[3rem] m-0">{t("header")}</h2>
            <div className="flex flex-col font-poppins">
              <p className="font-bold text-[1.5rem] my-4 mx-0 mb-[-0.8rem]">{t("addressLabel")}</p>
              <p>{t("address")}</p>
            </div>
            <div className="flex flex-col font-poppins">
              <p className="font-bold text-[1.5rem] my-4 mx-0 mb-[-0.8rem]">{t("emailLabel")}</p>
              <p>{t("email")}</p>
            </div>
          </div>
          <div className="flex w-[95%] md:w-1/2 justify-center flex-col items-center">
            <Image alt={t("mascotsAlt")} src={Mascots} className="max-w-[90%] h-auto object-contain" />
          </div>
        </header>
        <section className="w-[95%] mx-auto">
          <h1 className="text-center text-[2rem]">{t("formTitle")}</h1>
          <form>
            <div className="flex flex-col md:flex-row justify-between">
              <div className="w-full md:w-[49%]">
                <input
                  placeholder={t("form.firstName")}
                  type="text"
                  id="firstName"
                  name="firstName"
                  className="h-[52px] border-none bg-transparent border-b border-black py-0 px-4 text-[1.3rem] text-[#5b5b5b] w-full my-4 mx-0 focus:outline-none placeholder:text-[1.3rem] placeholder:text-[#5b5b5b] box-border"
                />
              </div>
              <div className="w-full md:w-[49%]">
                <input
                  placeholder={t("form.email")}
                  type="email"
                  id="email"
                  name="email"
                  className="h-[52px] border-none bg-transparent border-b border-black py-0 px-4 text-[1.3rem] text-[#5b5b5b] w-full my-4 mx-0 focus:outline-none placeholder:text-[1.3rem] placeholder:text-[#5b5b5b] box-border"
                />
              </div>
            </div>
            <div className="w-full">
              <select
                id="category"
                name="category"
                className="h-[52px] border-none bg-transparent border-b border-black py-0 px-4 text-[1.3rem] text-[#5b5b5b] w-full my-4 mx-0 focus:outline-none"
              >
                <option value="">{t("form.selectCategory")}</option>
                <option value="general">{t("form.general")}</option>
                <option value="support">{t("form.support")}</option>
                <option value="sales">{t("form.sales")}</option>
              </select>
            </div>
            <div className="w-full">
              <textarea
                placeholder={t("form.message")}
                id="message"
                name="message"
                rows="10"
                className="bg-transparent border border-black py-4 px-4 text-[1.3rem] text-[#5b5b5b] w-full my-8 mx-0 rounded-lg box-border focus:outline-none placeholder:text-[1.3rem] placeholder:text-[#5b5b5b]"
              ></textarea>
            </div>
            <div className="flex items-center">
              <input type="checkbox" id="privacyPolicy" name="privacyPolicy" className="cursor-pointer mr-[0.3rem]" />
              <label htmlFor="privacyPolicy">
                {t("form.privacyPolicy")} <a href="#" className="text-blue-600 underline">{t("form.privacyLink")}</a>
              </label>
            </div>
            <button type="submit" className="mt-8 bg-[#9258fe] text-[1.2rem] text-white rounded-[0.8rem] py-[0.8rem] px-[1.5rem] no-underline border-none shadow-[inset_0px_5px_5px_rgba(203,177,252,0.9),0px_5px_6px_rgba(0,0,0,0.978)] cursor-pointer mr-20">
              {t("form.submit")}
            </button>
          </form>
        </section>
      </div>
    </>
  );
};

export default ContactUs;
