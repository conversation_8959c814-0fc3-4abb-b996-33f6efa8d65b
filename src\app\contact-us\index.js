import { useTranslations } from "next-intl";
import Head from "next/head";
import Image from "next/image";
import Mascots from "../../../public/images/contactUs/contactUsMascots.webp";

const ContactUs = () => {
  const t = useTranslations("ContactUs");

  return (
    <>
      <Head>
        <title>{t("title")}</title>
        <meta name="description" content={t("metaDescription")} />
      </Head>
      <div className="container-main">
        <header className="max-w-full bg-[url('/images/contactUs/contactUsBanner.webp')] bg-cover bg-center bg-no-repeat flex object-cover flex-col md:flex-row">
          <div className="flex w-[95%] md:w-1/2 justify-center flex-col mx-auto md:px-12 md:my-auto">
            <h2 className="heading-primary m-0">{t("header")}</h2>
            <div className="flex flex-col">
              <p className="text-subheading my-4 mx-0 mb-[-0.8rem]">{t("addressLabel")}</p>
              <p className="text-body">{t("address")}</p>
            </div>
            <div className="flex flex-col">
              <p className="text-subheading my-4 mx-0 mb-[-0.8rem]">{t("emailLabel")}</p>
              <p className="text-body">{t("email")}</p>
            </div>
          </div>
          <div className="flex w-[95%] md:w-1/2 justify-center flex-col items-center">
            <Image alt={t("mascotsAlt")} src={Mascots} className="max-w-[90%] h-auto object-contain" />
          </div>
        </header>
        <section className="section-padding w-[95%] mx-auto">
          <h1 className="heading-secondary text-center">{t("formTitle")}</h1>
          <form>
            <div className="flex flex-col md:flex-row justify-between">
              <div className="w-full md:w-[49%]">
                <input
                  placeholder={t("form.firstName")}
                  type="text"
                  id="firstName"
                  name="firstName"
                  className="form-input-underline"
                />
              </div>
              <div className="w-full md:w-[49%]">
                <input
                  placeholder={t("form.email")}
                  type="email"
                  id="email"
                  name="email"
                  className="form-input-underline"
                />
              </div>
            </div>
            <div className="w-full">
              <select
                id="category"
                name="category"
                className="form-input-underline"
              >
                <option value="">{t("form.selectCategory")}</option>
                <option value="general">{t("form.general")}</option>
                <option value="support">{t("form.support")}</option>
                <option value="sales">{t("form.sales")}</option>
              </select>
            </div>
            <div className="w-full">
              <textarea
                placeholder={t("form.message")}
                id="message"
                name="message"
                rows="10"
                className="form-textarea"
              ></textarea>
            </div>
            <div className="flex items-center">
              <input type="checkbox" id="privacyPolicy" name="privacyPolicy" className="cursor-pointer mr-[0.3rem]" />
              <label htmlFor="privacyPolicy" className="text-body">
                {t("form.privacyPolicy")} <a href="#" className="text-primary-400 underline">{t("form.privacyLink")}</a>
              </label>
            </div>
            <FormCtaButton
              text={t("form.submit")}
              customStyles="mt-8 mr-20"
            />
          </form>
        </section>
      </div>
    </>
  );
};

export default ContactUs;
