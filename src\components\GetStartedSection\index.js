"use client";
import CarouselGetStarted from "@/components/CarouselGetStarted";
// import styles from "./styles.module.css";
import FormCtaButton from "@/components/FormCtaButton";
import { carouselDataGetStarted } from "@/constants";
import { getLocale } from "@/utils/helperFunctions";
import { useLocale, useTranslations } from "next-intl";
import { useRouter } from "next/navigation";
import { useState } from "react";

const GetStarted = ({ onGetStarted }) => {
  const t = useTranslations("GetStarted");
  const carouselData = carouselDataGetStarted.map((item) => ({
    ...item,
    subHeading: t(item.key),
  }));
  const [currentSubHeading, setCurrentSubHeading] = useState(carouselData[0].subHeading);
  const router = useRouter();
  const locale = useLocale();
  const lang = getLocale(locale);

  const handleSlideChange = (currentSlide) => {
    setCurrentSubHeading(carouselData[currentSlide].subHeading);
  };

  const redirectToLogin = () => {
    router.push("/login");
  };

  return (
    <div className="max-w-[480px] mx-auto text-center max-[750px]:max-w-full">
      <CarouselGetStarted onSlideChange={handleSlideChange} />
      <h1 className="my-4 mx-0 mt-4 mb-2">{t("Greeting")}</h1>
      <p className="m-0 text-2xl font-poppins text-black/70 font-medium">{currentSubHeading}</p>
      <FormCtaButton text={t("CtaText")} onClick={onGetStarted} />
      <p className="mt-6 mb-0">
        {t("LoginMessage")}
        <span className="text-[#0078ff] cursor-pointer" onClick={redirectToLogin}>
          {" "}
          {t("LoginCta")}
        </span>
      </p>
    </div>
  );
};

export default GetStarted;
