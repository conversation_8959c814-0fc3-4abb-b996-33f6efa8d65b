"use client";
import { HomeTilesShimmer, UserImgShimmer } from "@/components/HomeScreenShimmer";
import ScreenRotationOverlay from "@/components/prompt/page";
import UserHomeScreenPopup from "@/components/UserHomeScreenPopup";
import { useAuth } from "@/context/AuthContext";
import apiClient from "@/utils/axiosUtil";
import { extractGameData, getLocale } from "@/utils/helperFunctions";
import { trackWebEngageEvent } from "@/utils/webengage";
import { useLocale, useTranslations } from "next-intl";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useEffect, useRef, useState } from "react";

const UserHomeScreen = () => {
  const [isOverlayOpen, setOverlayOpen] = useState(false);
  const [isDark, setDarkTheme] = useState(false);
  const [selectedCard, setSelectedCard] = useState(null);
  const [isLandscape, setIsLandscape] = useState(window.innerWidth > window.innerHeight);
  const [homeScreenData, setHomeScreenData] = useState(null);
  const [playerData, setPlayerData] = useState([]);
  const { isSubscribed } = useAuth();

  const locale = useLocale();
  const t = useTranslations("UserHomeScree");
  const lang = getLocale(locale);
  const router = useRouter();

  if (!isSubscribed) {
    router.replace("/products");
  }
  useEffect(() => {
    trackWebEngageEvent("WebGlHomepageReached");
  }, []);

  const openOverlay = (cardData) => {
    setSelectedCard(cardData);
    setOverlayOpen(true);
  };

  const closeOverlay = () => {
    setOverlayOpen(false);
    setSelectedCard(null);
  };
  const pageRef = useRef();

  const bgImgLight = "/images/webGl/userHomeScreen/bgLight.png";
  const bgImgDark = "/images/webGl/userHomeScreen/bgDark.png";
  const natureTheme = "/images/webGl/userHomeScreen/natureTheme.webp";
  const spaceTheme = "/images/webGl/userHomeScreen/spaceTheme.webp";

  useEffect(() => {
    const footer = document.getElementById("footer");
    if (footer) footer.style.display = "none";
    const checkOrientation = () => {
      setIsLandscape(window.innerWidth > window.innerHeight);
    };

    // Check on mount
    checkOrientation();
    // Add event listener for resize
    window.addEventListener("resize", checkOrientation);
    fetchHomeScreenTiles();
    fetchPlayerData();

    return () => {
      // Clean up the event listener
      window.removeEventListener("resize", checkOrientation);
      if (footer) footer.style.display = "block";
    };
  }, [lang]);

  const fetchHomeScreenTiles = async () => {
    const url = `${process.env.NEXT_PUBLIC_PRODUCTSERVICE_BASE_URL}/homescreen/content`;

    const params = new URLSearchParams({
      gameid: "web",
      grade: "0",
      player_id: localStorage.getItem("playerId"),
      l: lang,
      ram: "65536",
      version: "8.1",
      platform: "ios",
      is_web_enabled: "true",
    });

    try {
      const response = await apiClient.post(`${url}?${params}`, {});
      const data = response.data;
      setHomeScreenData(data);
    } catch (error) {
      console.error("Error fetching home screen data:", error);
    }
  };

  async function fetchPlayerData() {
    const url = `${process.env.NEXT_PUBLIC_USERSERVICE_BASE_URL}/player?language=${lang}&gameid=doctor&version=8.2&platform=iOS&p=unity&sessionID=9a9d4b7b-59e6-48f3-8ec4-8f6cbcaf8d85&appsgroup=False&did=&ornt=landscape`;

    try {
      const response = await apiClient.get(`${url}`);
      const data = response.data;
      setPlayerData(data);
    } catch (error) {
      console.error("Error fetching home screen data:", error);
    }
  }

  if (!isLandscape) {
    return <ScreenRotationOverlay isOpen={true} />;
  }

  const toggleTheme = () => {
    setDarkTheme((prev) => !prev);
  };

  const handleGameClick = () => {
    trackWebEngageEvent("WebGlGameClk", { current_game: homeScreenData.DisplayName });
    router.push(
      `/game-player?gameUrl=${extractGameData(homeScreenData.WebGameUrl).gameUrl}&gameName=${homeScreenData.DisplayName}&gameOrientation=${extractGameData(homeScreenData.WebGameUrl).gameOrientation}`
    );
  };

  return (
    <div
      ref={pageRef}
      className={`user-home-screen ${isDark ? "dark" : ""}`}
    >
      <Image
        src={bgImgLight}
        alt="Light Background"
        layout="fill"
        objectFit="cover"
        priority
        className={`user-home-bg ${isDark ? "opacity-0" : "opacity-100"}`}
      />
      <Image
        src={bgImgDark}
        alt="Dark Background"
        layout="fill"
        objectFit="cover"
        priority
        className={`user-home-bg ${isDark ? "opacity-100" : "opacity-0"}`}
      />
      <div className="relative z-[1]">
        <div className="user-home-header">
          {playerData?.players?.length > 0 ? (
            <div className="user-greeting">
              <div>
                <Image
                  src={`/images/webGl/userHomeScreen/homeScreenAvatar/${playerData.players[0].AvatarIndex}.webp`}
                  alt="User Profile"
                  width={80}
                  height={80}
                  className="user-avatar"
                />
              </div>
              <div className="user-greeting-text" style={{ color: isDark ? "#ffff" : "black" }}>
                {t("Greeting")}
                <Image
                  src="/images/webGl/userHomeScreen/wavingHand.webp"
                  alt="Waving Hand"
                  width={40}
                  height={40}
                  className="wavingHand"
                />
                , {playerData?.players[0].Nickname}
              </div>
            </div>
          ) : (
            <UserImgShimmer />
          )}
          <div className="theme-toggle" onClick={toggleTheme}>
            <div className="relative w-full h-full">
              <Image
                src={spaceTheme}
                className={`theme-toggle-image ${isDark ? "opacity-0" : "opacity-100"}`}
                width={115}
                height={65}
                alt="Space theme"
                priority
              />
              <Image
                src={natureTheme}
                className={`theme-toggle-image ${isDark ? "opacity-100" : "opacity-0"}`}
                width={115}
                height={65}
                alt="Nature theme"
                priority
              />
            </div>
          </div>
        </div>
        <div className="games-container">
          {homeScreenData?.DisplayName && (
            <div className="hero-game" onClick={handleGameClick}>
              <div className="hero-game-title">{homeScreenData?.DisplayName}</div>
              <Image
                src={homeScreenData.IconUrl}
                width={420}
                height={420}
                className="heroGameImage"
                alt={homeScreenData?.DisplayName}
              />
              <Image
                src="/images/webGl/userHomeScreen/playBtn.png"
                width={90}
                height={90}
                className="hero-play-btn"
                alt="Play button"
              />
            </div>
          )}
          <div>
            <div className="game-tiles-row">
              {homeScreenData?.Tiles ? (
                homeScreenData.Tiles.map(
                  (item, index) =>
                    index % 2 === 0 && (
                      <div
                        className="game-tile"
                        onClick={() => openOverlay(item)}
                        key={index}
                      >
                        <Image
                          src={item.IconURL}
                          fill
                          className="object-cover max-h-[160px] rounded-t-[10px]"
                          alt={item.TileName}
                        />
                        <div className="game-tile-overlay">
                          <span className="game-tile-title">{item.TileName}</span>
                        </div>
                      </div>
                    )
                )
              ) : (
                <HomeTilesShimmer />
              )}
            </div>
            <div className="game-tiles-row game-tiles-row-offset">
              {homeScreenData?.Tiles ? (
                homeScreenData.Tiles.map(
                  (item, index) =>
                    index % 2 !== 0 && (
                      <div
                        className="game-tile"
                        onClick={() => openOverlay(item)}
                        key={index}
                      >
                        <Image
                          src={item.IconURL}
                          fill
                          className="object-cover max-h-[160px] rounded-t-[10px]"
                          alt={item.TileName}
                        />
                        <div className="game-tile-overlay">
                          <span className="game-tile-title">{item.TileName}</span>
                        </div>
                      </div>
                    )
                )
              ) : (
                <HomeTilesShimmer />
              )}
            </div>
          </div>
        </div>
      </div>
      <UserHomeScreenPopup
        isOpen={isOverlayOpen}
        onClose={closeOverlay}
        isDark={isDark}
        cardData={selectedCard}
      />
    </div>
  );
};

export default UserHomeScreen;
