"use client";
import { HomeTilesShimmer, UserImgShimmer } from "@/components/HomeScreenShimmer";
import ScreenRotationOverlay from "@/components/prompt/page";
import UserHomeScreenPopup from "@/components/UserHomeScreenPopup";
import { useAuth } from "@/context/AuthContext";
import apiClient from "@/utils/axiosUtil";
import { extractGameData, getLocale } from "@/utils/helperFunctions";
import { trackWebEngageEvent } from "@/utils/webengage";
import { useLocale, useTranslations } from "next-intl";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useEffect, useRef, useState } from "react";

const UserHomeScreen = () => {
  const [isOverlayOpen, setOverlayOpen] = useState(false);
  const [isDark, setDarkTheme] = useState(false);
  const [selectedCard, setSelectedCard] = useState(null);
  const [isLandscape, setIsLandscape] = useState(window.innerWidth > window.innerHeight);
  const [homeScreenData, setHomeScreenData] = useState(null);
  const [playerData, setPlayerData] = useState([]);
  const { isSubscribed } = useAuth();

  const locale = useLocale();
  const t = useTranslations("UserHomeScree");
  const lang = getLocale(locale);
  const router = useRouter();

  if (!isSubscribed) {
    router.replace("/products");
  }
  useEffect(() => {
    trackWebEngageEvent("WebGlHomepageReached");
  }, []);

  const openOverlay = (cardData) => {
    setSelectedCard(cardData);
    setOverlayOpen(true);
  };

  const closeOverlay = () => {
    setOverlayOpen(false);
    setSelectedCard(null);
  };
  const pageRef = useRef();

  const bgImgLight = "/images/webGl/userHomeScreen/bgLight.png";
  const bgImgDark = "/images/webGl/userHomeScreen/bgDark.png";
  const natureTheme = "/images/webGl/userHomeScreen/natureTheme.webp";
  const spaceTheme = "/images/webGl/userHomeScreen/spaceTheme.webp";

  useEffect(() => {
    const footer = document.getElementById("footer");
    if (footer) footer.style.display = "none";
    const checkOrientation = () => {
      setIsLandscape(window.innerWidth > window.innerHeight);
    };

    // Check on mount
    checkOrientation();
    // Add event listener for resize
    window.addEventListener("resize", checkOrientation);
    fetchHomeScreenTiles();
    fetchPlayerData();

    return () => {
      // Clean up the event listener
      window.removeEventListener("resize", checkOrientation);
      if (footer) footer.style.display = "block";
    };
  }, [lang]);

  const fetchHomeScreenTiles = async () => {
    const url = `${process.env.NEXT_PUBLIC_PRODUCTSERVICE_BASE_URL}/homescreen/content`;

    const params = new URLSearchParams({
      gameid: "web",
      grade: "0",
      player_id: localStorage.getItem("playerId"),
      l: lang,
      ram: "65536",
      version: "8.1",
      platform: "ios",
      is_web_enabled: "true",
    });

    try {
      const response = await apiClient.post(`${url}?${params}`, {});
      const data = response.data;
      setHomeScreenData(data);
    } catch (error) {
      console.error("Error fetching home screen data:", error);
    }
  };

  async function fetchPlayerData() {
    const url = `${process.env.NEXT_PUBLIC_USERSERVICE_BASE_URL}/player?language=${lang}&gameid=doctor&version=8.2&platform=iOS&p=unity&sessionID=9a9d4b7b-59e6-48f3-8ec4-8f6cbcaf8d85&appsgroup=False&did=&ornt=landscape`;

    try {
      const response = await apiClient.get(`${url}`);
      const data = response.data;
      setPlayerData(data);
    } catch (error) {
      console.error("Error fetching home screen data:", error);
    }
  }

  if (!isLandscape) {
    return <ScreenRotationOverlay isOpen={true} />;
  }

  const toggleTheme = () => {
    setDarkTheme((prev) => !prev);
  };

  const handleGameClick = () => {
    trackWebEngageEvent("WebGlGameClk", { current_game: homeScreenData.DisplayName });
    router.push(
      `/game-player?gameUrl=${extractGameData(homeScreenData.WebGameUrl).gameUrl}&gameName=${homeScreenData.DisplayName}&gameOrientation=${extractGameData(homeScreenData.WebGameUrl).gameOrientation}`
    );
  };

  return (
    <div
      ref={pageRef}
      className={`bg-cover bg-center bg-no-repeat min-h-[90vh] w-full overflow-hidden box-border max-w-[1728px] mx-auto relative transition-all duration-300 ease-in-out ${isDark ? "dark" : ""}`}
    >
      <Image
        src={bgImgLight}
        alt="Light Background"
        layout="fill"
        objectFit="cover"
        priority
        className={`absolute top-0 left-0 w-full h-full object-cover transition-opacity duration-300 ease-in-out ${isDark ? "opacity-0" : "opacity-100"}`}
      />
      <Image
        src={bgImgDark}
        alt="Dark Background"
        layout="fill"
        objectFit="cover"
        priority
        className={`absolute top-0 left-0 w-full h-full object-cover transition-opacity duration-300 ease-in-out ${isDark ? "opacity-100" : "opacity-0"}`}
      />
      <div className="relative z-[1]">
        <div className="flex items-center text-[2.1rem] gap-[10px] font-poppins font-semibold p-4 px-8 justify-between">
          {playerData?.players?.length > 0 ? (
            <div className="flex items-center gap-[10px]">
              <div>
                <Image
                  src={`/images/webGl/userHomeScreen/homeScreenAvatar/${playerData.players[0].AvatarIndex}.webp`}
                  alt="User Profile"
                  width={80}
                  height={80}
                  className="h-20 w-20 object-cover"
                />
              </div>
              <div style={{ color: isDark ? "#ffff" : "black" }}>
                {t("Greeting")}
                <Image
                  src="/images/webGl/userHomeScreen/wavingHand.webp"
                  alt="Waving Hand"
                  width={40}
                  height={40}
                  className="wavingHand"
                />
                , {playerData?.players[0].Nickname}
              </div>
            </div>
          ) : (
            <UserImgShimmer />
          )}
          <div className="cursor-pointer relative w-[115px] h-[65px] overflow-hidden" onClick={toggleTheme}>
            <div className="relative w-full h-full">
              <Image
                src={spaceTheme}
                className={`absolute top-0 left-0 transition-opacity duration-300 ease-in-out cursor-pointer ${isDark ? "opacity-0" : "opacity-100"}`}
                width={115}
                height={65}
                alt="Space theme"
                priority
              />
              <Image
                src={natureTheme}
                className={`absolute top-0 left-0 transition-opacity duration-300 ease-in-out cursor-pointer ${isDark ? "opacity-100" : "opacity-0"}`}
                width={115}
                height={65}
                alt="Nature theme"
                priority
              />
            </div>
          </div>
        </div>
        <div className="overflow-x-auto overflow-y-hidden flex items-center p-4 scrollbar-none">
          {homeScreenData?.DisplayName && (
            <div className="relative" onClick={handleGameClick}>
              <div className="absolute text-[1.8rem] top-3 left-[38%] text-white">{homeScreenData?.DisplayName}</div>
              <Image
                src={homeScreenData.IconUrl}
                width={420}
                height={420}
                className="heroGameImage"
                alt={homeScreenData?.DisplayName}
              />
              <Image
                src="/images/webGl/userHomeScreen/playBtn.png"
                width={90}
                height={90}
                className="absolute -bottom-[30px] left-1/2 transform -translate-x-1/2 z-10 animate-pulse"
                alt="Play button"
              />
            </div>
          )}
          <div>
            <div className="my-4 flex gap-5">
              {homeScreenData?.Tiles ? (
                homeScreenData.Tiles.map(
                  (item, index) =>
                    index % 2 === 0 && (
                      <div
                        className="w-[290px] h-[200px] transform skew-x-[10deg] relative rounded-[14px] overflow-hidden shadow-[0px_6.13px_0px_0px_#4a2d99] bg-gradient-radial from-[#c3b1ff] to-[#7a4bf8] flex-shrink-0 box-border cursor-pointer font-poppins font-semibold"
                        onClick={() => openOverlay(item)}
                        key={index}
                      >
                        <Image
                          src={item.IconURL}
                          fill
                          className="object-cover max-h-[160px] rounded-t-[10px]"
                          alt={item.TileName}
                        />
                        <div className="absolute text-white bottom-0 p-0 px-[0.63rem] overflow-hidden rounded-bl-[10px] left-0 w-full bg-gradient-to-t from-black to-transparent h-10 flex items-center box-border">
                          <span className="block transform -skew-x-[10deg]">{item.TileName}</span>
                        </div>
                      </div>
                    )
                )
              ) : (
                <HomeTilesShimmer />
              )}
            </div>
            <div className="my-4 flex gap-5 pl-[2.3rem]">
              {homeScreenData?.Tiles ? (
                homeScreenData.Tiles.map(
                  (item, index) =>
                    index % 2 !== 0 && (
                      <div
                        className="w-[290px] h-[200px] transform skew-x-[10deg] relative rounded-[14px] overflow-hidden shadow-[0px_6.13px_0px_0px_#4a2d99] bg-gradient-radial from-[#c3b1ff] to-[#7a4bf8] flex-shrink-0 box-border cursor-pointer font-poppins font-semibold"
                        onClick={() => openOverlay(item)}
                        key={index}
                      >
                        <Image
                          src={item.IconURL}
                          fill
                          className="object-cover max-h-[160px] rounded-t-[10px]"
                          alt={item.TileName}
                        />
                        <div className="absolute text-white bottom-0 p-0 px-[0.63rem] overflow-hidden rounded-bl-[10px] left-0 w-full bg-gradient-to-t from-black to-transparent h-10 flex items-center box-border">
                          <span className="block transform -skew-x-[10deg]">{item.TileName}</span>
                        </div>
                      </div>
                    )
                )
              ) : (
                <HomeTilesShimmer />
              )}
            </div>
          </div>
        </div>
      </div>
      <UserHomeScreenPopup
        isOpen={isOverlayOpen}
        onClose={closeOverlay}
        isDark={isDark}
        cardData={selectedCard}
      />
    </div>
  );
};

export default UserHomeScreen;
