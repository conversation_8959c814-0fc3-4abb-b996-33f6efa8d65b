"use client";
import { useAuth } from "@/context/AuthContext";
import { trackWebEngageEvent } from "@/utils/webengage";
import { useTranslations } from "next-intl";
import Image from "next/image";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useState } from "react";
import Cross from "../../../public/images/cross.png";
import skidosLogo from "../../../public/images/skidosLogo.png";
import LanguageSelector from "../LanguageSelector";
// import styles from "./styles.module.css";

const NavbarHome = () => {
  const pathname = usePathname();
  const [isOpen, setIsOpen] = useState(false);
  const t = useTranslations("NavbarHome");
  const getLinkClass = (pathnames) => {
    return pathname === pathnames ? "text-black after:w-full" : "";
  };
  const specificPages = ["/user-home-screen/", "/game-player/"];
  const isSpecificPage = specificPages.includes(pathname);

  const toggleMenu = () => {
    setIsOpen(!isOpen);
    if (!isOpen) {
      document.body.classList.add("overflow-hidden", "h-full");
    } else {
      document.body.classList.remove("overflow-hidden", "h-full");
    }
  };
  const { isLoggedIn, isSubscribed } = useAuth();

  const excludedPaths = [
    "/",
    // "/login/",
    "/profile/",
    // "/login-otp/",
    "/about-us/",
    "/products/",
    "/blogs/",
    "/news/",
    "/terms/",
    "/privacy-policy/",
    "/partnership/",
    // "/user-home-screen/",
    // "/acquisition/",
    // "/purchase/",
    "/career/",
    "/acquisition_success/",
    // "/get-started/"
  ];

  const showLanguageSelector = true;

  return (
    <nav
      className={`flex justify-between items-center p-3 bg-white border-b border-gray-300 sticky top-0 z-[100] nav-link w-full box-border ${isSpecificPage ? "max-[500px]:landscape:hidden" : ""}`}
      id="navbar"
    >
      <div className="logo">
        <Link href="/">
          <Image
            src={skidosLogo}
            alt={t("LogoAlt")}
            width={167}
            height={52}
            className="max-[820px]:w-[140px] max-[820px]:h-[45px]"
          />
        </Link>
      </div>
      <div
        className={`hidden max-[820px]:block cursor-pointer ${isOpen ? "[&>div:nth-child(1)]:rotate-[-45deg] [&>div:nth-child(1)]:translate-x-[-5px] [&>div:nth-child(1)]:translate-y-[6px] [&>div:nth-child(2)]:opacity-0 [&>div:nth-child(3)]:rotate-[45deg] [&>div:nth-child(3)]:translate-x-[-5px] [&>div:nth-child(3)]:translate-y-[-6px]" : ""}`}
        onClick={toggleMenu}
      >
        <div className="w-[30px] h-[3px] bg-black my-1 transition-all duration-[0.4s]"></div>
        <div className="w-[30px] h-[3px] bg-black my-1 transition-all duration-[0.4s]"></div>
        <div className="w-[30px] h-[3px] bg-black my-1 transition-all duration-[0.4s]"></div>
      </div>
      <ul
        className={`flex list-none m-0 p-0 items-center flex-nowrap max-[820px]:flex-col max-[820px]:w-full max-[820px]:h-screen max-[820px]:fixed max-[820px]:top-0 max-[820px]:bg-white max-[820px]:items-center max-[820px]:transition-all max-[820px]:duration-300 max-[820px]:overflow-y-auto max-[820px]:overflow-x-hidden max-[820px]:p-4 max-[820px]:box-border ${isOpen ? "max-[820px]:right-0 max-[820px]:z-20" : "max-[820px]:right-[-150%]"}`}
      >
        <li className="ml-4 whitespace-nowrap max-[820px]:my-4 max-[820px]:text-2xl max-[820px]:text-center max-[820px]:ml-0">
          <Image
            src={Cross}
            width={41}
            height={41}
            className="hidden max-[820px]:block"
            onClick={toggleMenu}
            alt={t("CloseAlt")}
          />
        </li>
        <li className="ml-4 whitespace-nowrap max-[820px]:my-4 max-[820px]:text-2xl max-[820px]:text-center max-[820px]:ml-0">
          <Link
            href="/"
            className={`nav-link py-2 px-2 block relative whitespace-nowrap max-[820px]:text-xl max-[820px]:p-2 after:content-[''] after:absolute after:bottom-0 after:left-1/2 after:transform after:-translate-x-1/2 after:w-0 after:h-1 after:bg-primary-400 after:rounded-sm after:transition-all after:duration-300 after:ease-in-out hover:after:w-full ${getLinkClass("/")}`}
            onClick={toggleMenu}
          >
            {t("Home")}
          </Link>
        </li>
        <li className="ml-4 whitespace-nowrap max-[820px]:my-4 max-[820px]:text-2xl max-[820px]:text-center max-[820px]:ml-0">
          <Link
            href="/about-us"
            className={`nav-link py-2 px-2 block relative whitespace-nowrap max-[820px]:text-xl max-[820px]:p-2 after:content-[''] after:absolute after:bottom-0 after:left-1/2 after:transform after:-translate-x-1/2 after:w-0 after:h-1 after:bg-primary-400 after:rounded-sm after:transition-all after:duration-300 after:ease-in-out hover:after:w-full ${getLinkClass("/about-us/")}`}
            onClick={toggleMenu}
          >
            {t("AboutUs")}
          </Link>
        </li>
        <li className="ml-4 whitespace-nowrap max-[820px]:my-4 max-[820px]:text-2xl max-[820px]:text-center max-[820px]:ml-0">
          <Link
            href="/products"
            className={`nav-link py-2 px-2 block relative whitespace-nowrap max-[820px]:text-xl max-[820px]:p-2 after:content-[''] after:absolute after:bottom-0 after:left-1/2 after:transform after:-translate-x-1/2 after:w-0 after:h-1 after:bg-primary-400 after:rounded-sm after:transition-all after:duration-300 after:ease-in-out hover:after:w-full ${getLinkClass("/products/")}`}
            onClick={toggleMenu}
          >
            {t("Products")}
          </Link>
        </li>
        <li className="ml-4 whitespace-nowrap max-[820px]:my-4 max-[820px]:text-2xl max-[820px]:text-center max-[820px]:ml-0">
          {isLoggedIn ? (
            <Link
              href="/profile"
              className={`text-gray-400 no-underline py-2 px-2 block relative whitespace-nowrap max-[820px]:text-xl max-[820px]:p-2 hover:text-black after:content-[''] after:absolute after:bottom-0 after:left-1/2 after:transform after:-translate-x-1/2 after:w-0 after:h-1 after:bg-primary-400 after:rounded-sm after:transition-all after:duration-300 after:ease-in-out hover:after:w-full ${getLinkClass("/profile/")}`}
              onClick={toggleMenu}
            >
              {t("Profile")}
            </Link>
          ) : (
            <Link
              href="/login"
              className={`text-gray-400 no-underline py-2 px-2 block relative whitespace-nowrap max-[820px]:text-xl max-[820px]:p-2 hover:text-black after:content-[''] after:absolute after:bottom-0 after:left-1/2 after:transform after:-translate-x-1/2 after:w-0 after:h-1 after:bg-primary-400 after:rounded-sm after:transition-all after:duration-300 after:ease-in-out hover:after:w-full ${getLinkClass("/login/")}`}
              onClick={toggleMenu}
            >
              {t("Login")}
            </Link>
          )}
        </li>
        {!(isLoggedIn && isSubscribed) && (
          <li className="ml-4 whitespace-nowrap max-[820px]:my-4 max-[820px]:text-2xl max-[820px]:text-center max-[820px]:ml-0">
            <Link
              href="/get-started"
              className="btn-primary text-2xl whitespace-nowrap hover:text-white max-[820px]:text-xl max-[820px]:inline-block max-[820px]:text-center"
              onClick={() => {
                toggleMenu();
                trackWebEngageEvent("WebGLFreeTrialClk");
              }}
            >
              {t("FreeTrial")}
            </Link>
          </li>
        )}
        {showLanguageSelector && (
          <li>
            <LanguageSelector />
          </li>
        )}
      </ul>
    </nav>
  );
};

export default NavbarHome;
