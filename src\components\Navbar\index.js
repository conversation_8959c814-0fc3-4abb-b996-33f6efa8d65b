"use client";
import { useAuth } from "@/context/AuthContext";
import { trackWebEngageEvent } from "@/utils/webengage";
import { useTranslations } from "next-intl";
import Image from "next/image";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useEffect, useState } from "react";
import Cross from "../../../public/images/cross.png";
import skidosLogo from "../../../public/images/skidosLogo.png";
import LanguageSelector from "../LanguageSelector";

const NavbarHome = () => {
  const pathname = usePathname();
  const [isOpen, setIsOpen] = useState(false);
  const t = useTranslations("NavbarHome");
  const { isLoggedIn, isSubscribed } = useAuth();

  // Check if current page is active
  const isActiveLink = (path) => {
    return pathname === path;
  };

  // Get CSS classes for active/inactive links
  const getLinkClass = (path) => {
    return isActiveLink(path) ? "nav-link active" : "nav-link";
  };

  // Handle mobile menu toggle
  const toggleMenu = () => {
    setIsOpen(!isOpen);
    if (!isOpen) {
      document.body.classList.add("overflow-hidden", "h-full");
    } else {
      document.body.classList.remove("overflow-hidden", "h-full");
    }
  };

  // Handle menu item click (close mobile menu)
  const handleMenuItemClick = () => {
    setIsOpen(false);
    document.body.classList.remove("overflow-hidden", "h-full");
  };

  // Handle free trial click with tracking
  const handleFreeTrialClick = () => {
    handleMenuItemClick();
    trackWebEngageEvent("WebGLFreeTrialClk");
  };

  // Check if navbar should be hidden on specific pages in landscape mode
  const specificPages = ["/user-home-screen/", "/game-player/"];
  const isSpecificPage = specificPages.includes(pathname);

  // Navigation items configuration
  const navItems = [
    { href: "/", label: t("Home"), path: "/" },
    { href: "/about-us", label: t("AboutUs"), path: "/about-us/" },
    { href: "/products", label: t("Products"), path: "/products/" },
  ];

  // Cleanup effect to remove body scroll lock when component unmounts
  useEffect(() => {
    return () => {
      document.body.classList.remove("overflow-hidden", "h-full");
    };
  }, []);

  return (
    <nav className={`navbar ${isSpecificPage ? "max-[500px]:landscape:hidden" : ""}`} id="navbar">
      {/* Logo */}
      <div className="logo">
        <Link href="/">
          <Image
            src={skidosLogo}
            alt={t("LogoAlt")}
            width={167}
            height={52}
            className="max-[820px]:w-[140px] max-[820px]:h-[45px]"
          />
        </Link>
      </div>

      {/* Mobile Hamburger Menu */}
      <button
        className={`nav-hamburger ${isOpen ? "active" : ""}`}
        onClick={toggleMenu}
        aria-label="Toggle navigation menu"
        aria-expanded={isOpen}
      >
        <div className="nav-hamburger-bar"></div>
        <div className="nav-hamburger-bar"></div>
        <div className="nav-hamburger-bar"></div>
      </button>
      {/* Navigation Menu */}
      <ul className={`nav-menu ${isOpen ? "open" : "closed"}`}>
        {/* Mobile Close Button */}
        <li className="nav-item">
          <Image
            src={Cross}
            width={41}
            height={41}
            className="hidden max-[820px]:block cursor-pointer"
            onClick={handleMenuItemClick}
            alt={t("CloseAlt")}
          />
        </li>

        {/* Navigation Links */}
        {navItems.map((item) => (
          <li key={item.href} className="nav-item">
            <Link
              href={item.href}
              className={getLinkClass(item.path)}
              onClick={handleMenuItemClick}
            >
              {item.label}
            </Link>
          </li>
        ))}
        {/* Profile/Login Link */}
        <li className="nav-item">
          {isLoggedIn ? (
            <Link
              href="/profile"
              className={getLinkClass("/profile/")}
              onClick={handleMenuItemClick}
            >
              {t("Profile")}
            </Link>
          ) : (
            <Link href="/login" className={getLinkClass("/login/")} onClick={handleMenuItemClick}>
              {t("Login")}
            </Link>
          )}
        </li>

        {/* Free Trial Button - Only show if not logged in or not subscribed */}
        {!(isLoggedIn && isSubscribed) && (
          <li className="nav-item">
            <Link href="/get-started" className="nav-free-trial-btn" onClick={handleFreeTrialClick}>
              {t("FreeTrial")}
            </Link>
          </li>
        )}

        {/* Language Selector */}
        <li className="nav-item">
          <LanguageSelector />
        </li>
      </ul>
    </nav>
  );
};

export default NavbarHome;
