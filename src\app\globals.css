@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --font-nevermind-bold: "NeverMindBold", sans-serif;
  --font-nevermind-light: "NeverMindLight", sans-serif;
  --font-poppins: "PoppinsFont", sans-serif;
  --font-nevermind-medium: "NeverMindMedium", sans-serif;
}

@layer base {
  html,
  body {
    margin: 0;
    padding: 0;
    font-family: var(--font-nevermind-bold);
  }

  /* Base font size for rem calculations */
  :root {
    font-size: 16px;
  }

  /* Responsive adjustments for smaller screens */
  @media screen and (max-width: 768px) {
    :root {
      font-size: 14px;
    }
  }

  @media screen and (max-width: 480px) {
    :root {
      font-size: 12px;
    }
  }

  /* Font Family Base Classes */
  .font-nevermind-bold {
    font-family: var(--font-nevermind-bold);
  }

  .font-nevermind-medium {
    font-family: var(--font-nevermind-medium);
  }

  .font-nevermind-light {
    font-family: var(--font-nevermind-light);
  }

  .font-poppins {
    font-family: var(--font-poppins);
  }

  /* Font Weight Classes */
  .font-weight-light {
    font-weight: 300;
  }

  .font-weight-normal {
    font-weight: 400;
  }

  .font-weight-medium {
    font-weight: 500;
  }

  .font-weight-semibold {
    font-weight: 600;
  }

  .font-weight-bold {
    font-weight: 700;
  }

  /* Font Size Classes with Responsive Scaling */
  .text-xs {
    font-size: 0.75rem;
    line-height: 1rem;
  }

  .text-sm {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }

  .text-base {
    font-size: 1rem;
    line-height: 1.5rem;
  }

  .text-lg {
    font-size: 1.125rem;
    line-height: 1.75rem;
  }

  .text-xl {
    font-size: 1.25rem;
    line-height: 1.75rem;
  }

  .text-2xl {
    font-size: 1.5rem;
    line-height: 2rem;
  }

  .text-3xl {
    font-size: 1.875rem;
    line-height: 2.25rem;
  }

  .text-4xl {
    font-size: 2.25rem;
    line-height: 2.5rem;
  }

  .text-5xl {
    font-size: 3rem;
    line-height: 1;
  }

  .text-6xl {
    font-size: 3.75rem;
    line-height: 1;
  }

  /* Responsive Font Size Classes */
  @media screen and (max-width: 768px) {
    .text-responsive-sm {
      font-size: 0.875rem;
    }
    .text-responsive-base {
      font-size: 1rem;
    }
    .text-responsive-lg {
      font-size: 1.125rem;
    }
    .text-responsive-xl {
      font-size: 1.25rem;
    }
    .text-responsive-2xl {
      font-size: 1.5rem;
    }
    .text-responsive-3xl {
      font-size: 1.875rem;
    }
  }

  @media screen and (max-width: 480px) {
    .text-responsive-sm {
      font-size: 0.75rem;
    }
    .text-responsive-base {
      font-size: 0.875rem;
    }
    .text-responsive-lg {
      font-size: 1rem;
    }
    .text-responsive-xl {
      font-size: 1.125rem;
    }
    .text-responsive-2xl {
      font-size: 1.25rem;
    }
    .text-responsive-3xl {
      font-size: 1.5rem;
    }
  }
}

@layer components {
  /* Layout Components */
  .container-main {
    @apply max-w-container mx-auto;
  }

  /* Typography Components - Using Global Font System */
  .heading-primary {
    @apply font-nevermind-bold text-5xl md:text-6xl font-weight-bold;
  }

  .heading-secondary {
    @apply font-nevermind-bold text-4xl md:text-5xl font-weight-bold;
  }

  .heading-tertiary {
    @apply font-nevermind-bold text-2xl md:text-3xl font-weight-bold;
  }

  .heading-quaternary {
    @apply font-nevermind-bold text-xl md:text-2xl font-weight-bold;
  }

  .text-body {
    @apply font-poppins text-base font-weight-normal text-black;
  }

  .text-body-lg {
    @apply font-poppins text-lg font-weight-normal text-black;
  }

  .text-body-sm {
    @apply font-poppins text-sm font-weight-normal text-brand-text-gray;
  }

  .text-subheading {
    @apply font-poppins text-lg font-weight-medium text-brand-text-gray;
  }

  .text-caption {
    @apply font-poppins text-sm font-weight-normal text-gray-600;
  }

  .text-label {
    @apply font-poppins text-base font-weight-medium text-gray-700;
  }

  /* Button Components - Using Global Font System */
  .btn-primary {
    @apply bg-primary-400 text-white font-nevermind-bold text-lg font-weight-bold px-6 py-3 rounded-xl
           shadow-button-inset border-none cursor-pointer transition-transform duration-200
           hover:-translate-y-1;
  }

  .btn-secondary {
    @apply bg-white text-brand-purple-dark font-nevermind-bold text-lg font-weight-bold px-6 py-3 rounded-xl
           border border-gray-300 cursor-pointer transition-all duration-200
           hover:bg-gray-50 hover:shadow-card-hover;
  }

  .btn-outline {
    @apply bg-transparent text-black font-nevermind-bold text-lg font-weight-bold px-6 py-3 rounded-xl
           border border-black cursor-pointer transition-all duration-200
           hover:bg-black hover:text-white;
  }

  .btn-small {
    @apply font-nevermind-bold text-sm font-weight-bold px-4 py-2 rounded-lg
           cursor-pointer transition-all duration-200;
  }

  .btn-large {
    @apply font-nevermind-bold text-xl font-weight-bold px-8 py-4 rounded-xl
           cursor-pointer transition-all duration-200;
  }

  /* Card Components */
  .card-base {
    @apply bg-white rounded-2xl shadow-card border border-gray-200 p-6;
  }

  .card-hover {
    @apply card-base transition-all duration-300 hover:shadow-card-hover hover:scale-105;
  }

  .card-game {
    @apply card-hover text-center;
  }

  .card-pricing {
    @apply card-hover flex flex-col items-center justify-between min-w-[180px] w-80 h-80
           md:w-64 md:h-72 lg:w-72 lg:h-80;
  }

  /* Form Components - Using Global Font System */
  .form-input {
    @apply w-full py-3 px-4 border border-gray-300 rounded-xl font-poppins text-base font-weight-normal
           transition-colors duration-200 focus:outline-none focus:border-primary-400
           placeholder:text-gray-500;
  }

  .form-input-error {
    @apply form-input border-red-500 text-red-500;
  }

  .form-label {
    @apply text-left text-gray-600 font-poppins text-base font-weight-medium mb-2;
  }

  .form-helper-text {
    @apply font-poppins text-sm font-weight-normal text-gray-500 mt-1;
  }

  .form-error-text {
    @apply font-poppins text-sm font-weight-normal text-red-500 mt-1;
  }

  /* Section Components */
  .section-padding {
    @apply py-16 px-8 md:py-20 md:px-12;
  }

  .section-heading {
    @apply text-center mb-8 md:mb-12;
  }

  /* Grid Components */
  .grid-auto-fit {
    @apply grid justify-items-center gap-8;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  }

  .grid-cards {
    @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6;
  }

  /* Responsive Text */
  .text-responsive-lg {
    @apply text-lg md:text-xl lg:text-2xl;
  }

  .text-responsive-xl {
    @apply text-xl md:text-2xl lg:text-3xl;
  }

  /* About Cards - Using Global Font System */
  .about-card {
    @apply w-80 text-center font-poppins font-weight-medium text-brand-purple-dark
           md:w-72;
  }

  .about-card-image {
    @apply md:w-72 md:h-52;
  }

  .about-card-text {
    @apply text-label;
  }

  /* Award Components */
  .awards-grid {
    @apply flex justify-center items-center gap-8 flex-wrap max-w-[90%] mx-auto;
  }

  .award-image {
    @apply w-[150px] h-[150px] object-contain;
  }

  .awards-grid-home {
    @apply grid grid-cols-2 gap-5 lg:grid-cols-7 lg:items-center;
  }

  .award-image-home {
    @apply md:w-20 md:h-20;
  }

  /* Review Components */
  .review-card {
    @apply pt-4 pr-0 pb-0 pl-0 rounded-lg bg-brand-bg-light flex flex-col items-center justify-between text-brand-purple-dark;
  }

  .review-content {
    @apply text-body my-4 mx-8 mb-2;
  }

  .review-footer {
    @apply bg-white rounded-b-lg border-t border-[#e0e0e0] pt-4 mt-4;
  }

  .review-author {
    @apply text-body-sm text-[#666] m-0;
  }

  /* Game Card Component */
  .card-game {
    @apply card-hover transition-transform duration-300 ease hover:scale-[1.02];
  }

  /* Grid Components */
  .grid-cards {
    @apply grid gap-[10px] w-full;
  }

  /* Section Components */
  .section-container {
    @apply max-w-container mx-auto;
  }

  .section-heading {
    @apply text-center mb-8;
  }

  /* Filter Components */
  .btn-filter {
    @apply rounded-[40px] border border-[#d2d2d2] text-[#737373] py-2 px-4 whitespace-nowrap cursor-pointer
           hover:bg-[#fc8800] hover:text-white hover:border-transparent transition-colors duration-200;
  }

  .btn-filter-active {
    @apply bg-[#fc8800] text-white border-transparent;
  }

  /* Product Card Components */
  .card-product {
    @apply card-hover max-w-[350px] flex flex-col justify-between;
  }

  .skill-tag {
    @apply text-caption bg-[#f7f7f7] py-2 px-4 rounded-[2.5rem] border border-[#e9e9e9];
  }
}

@layer utilities {
  .noLinkStyle {
    text-decoration: none;
    color: inherit;
  }

  .noScroll {
    overflow: hidden;
  }

  .scrollbar-none {
    scrollbar-width: none;
    -ms-overflow-style: none;
  }

  .scrollbar-none::-webkit-scrollbar {
    display: none;
  }
}

/* Custom scrollbar styles */
.scrollbar-none {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.scrollbar-none::-webkit-scrollbar {
  display: none;
}

/* Custom checkbox styles for EnterEmailSection */
input[type="checkbox"] + label::before {
  content: "";
  position: absolute;
  left: 3px;
  top: 0px;
  width: 22px;
  height: 22px;
  border: 2px solid #ccc;
  border-radius: 4px;
  background-color: white;
  box-sizing: border-box;
}

.checkbox-error input[type="checkbox"] + label::before {
  border: 2px solid red;
}

input[type="checkbox"]:checked + label::before {
  border: none;
  background-color: white;
}

input[type="checkbox"]:checked + label::after {
  content: "";
  position: absolute;
  left: 0px;
  top: -5px;
  width: 30px;
  height: 30px;
  background-image: url("data:image/svg+xml,%3Csvg width='31' height='30' viewBox='0 0 31 30' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M13.7615 20.3697L21.4671 12.6641C21.9362 12.195 21.9362 11.4344 21.4671 10.9653C20.9979 10.4961 20.2373 10.4961 19.7682 10.9653L13.7615 16.972L11.1525 14.363C10.6834 13.8939 9.92277 13.8939 9.45364 14.363C8.98451 14.8321 8.98451 15.5927 9.45364 16.0619L13.7615 20.3697ZM6.96602 26.1944C6.2986 26.1944 5.72726 25.9567 5.25198 25.4815C4.7767 25.0062 4.53906 24.4348 4.53906 23.7674V6.77876C4.53906 6.11135 4.7767 5.54 5.25198 5.06472C5.72726 4.58945 6.2986 4.35181 6.96602 4.35181H23.9547C24.6221 4.35181 25.1934 4.58945 25.6687 5.06472C26.144 5.54 26.3816 6.11135 26.3816 6.77876V23.7674C26.3816 24.4348 26.144 25.0062 25.6687 25.4815C25.1934 25.9567 24.6221 26.1944 23.9547 26.1944H6.96602ZM6.96602 23.7674H23.9547V6.77876H6.96602V23.7674Z' fill='%231DC368'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}
