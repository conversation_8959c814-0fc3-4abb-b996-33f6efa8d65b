@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --font-nevermind-bold: "NeverMindBold", sans-serif;
  --font-nevermind-light: "NeverMindLight", sans-serif;
  --font-poppins: "PoppinsFont", sans-serif;
  --font-nevermind-medium: "NeverMindMedium", sans-serif;
}

@layer base {
  html,
  body {
    margin: 0;
    padding: 0;
    font-family: var(--font-nevermind-bold);
  }

  /* Base font size for rem calculations */
  :root {
    font-size: 16px;
  }

  /* Responsive adjustments for smaller screens */
  @media screen and (max-width: 768px) {
    :root {
      font-size: 14px;
    }
  }

  @media screen and (max-width: 480px) {
    :root {
      font-size: 12px;
    }
  }

  /* Font Family Base Classes */
  .font-nevermind-bold {
    font-family: var(--font-nevermind-bold);
  }

  .font-nevermind-medium {
    font-family: var(--font-nevermind-medium);
  }

  .font-nevermind-light {
    font-family: var(--font-nevermind-light);
  }

  .font-poppins {
    font-family: var(--font-poppins);
  }

  /* Font Weight Classes */
  .font-weight-light {
    font-weight: 300;
  }

  .font-weight-normal {
    font-weight: 400;
  }

  .font-weight-medium {
    font-weight: 500;
  }

  .font-weight-semibold {
    font-weight: 600;
  }

  .font-weight-bold {
    font-weight: 700;
  }

  /* Font Size Classes with Responsive Scaling */
  .text-xs {
    font-size: 0.75rem;
    line-height: 1rem;
  }

  .text-sm {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }

  .text-base {
    font-size: 1rem;
    line-height: 1.5rem;
  }

  .text-lg {
    font-size: 1.125rem;
    line-height: 1.75rem;
  }

  .text-xl {
    font-size: 1.25rem;
    line-height: 1.75rem;
  }

  .text-2xl {
    font-size: 1.5rem;
    line-height: 2rem;
  }

  .text-3xl {
    font-size: 1.875rem;
    line-height: 2.25rem;
  }

  .text-4xl {
    font-size: 2.25rem;
    line-height: 2.5rem;
  }

  .text-5xl {
    font-size: 3rem;
    line-height: 1;
  }

  .text-6xl {
    font-size: 3.75rem;
    line-height: 1;
  }

  /* Responsive Font Size Classes */
  @media screen and (max-width: 768px) {
    .text-responsive-sm {
      font-size: 0.875rem;
    }
    .text-responsive-base {
      font-size: 1rem;
    }
    .text-responsive-lg {
      font-size: 1.125rem;
    }
    .text-responsive-xl {
      font-size: 1.25rem;
    }
    .text-responsive-2xl {
      font-size: 1.5rem;
    }
    .text-responsive-3xl {
      font-size: 1.875rem;
    }
  }

  @media screen and (max-width: 480px) {
    .text-responsive-sm {
      font-size: 0.75rem;
    }
    .text-responsive-base {
      font-size: 0.875rem;
    }
    .text-responsive-lg {
      font-size: 1rem;
    }
    .text-responsive-xl {
      font-size: 1.125rem;
    }
    .text-responsive-2xl {
      font-size: 1.25rem;
    }
    .text-responsive-3xl {
      font-size: 1.5rem;
    }
  }
}

@layer components {
  /* Layout Components */
  .container-main {
    @apply max-w-container mx-auto;
  }

  /* Typography Components - Using Global Font System */
  .heading-primary {
    @apply font-nevermind-bold text-5xl md:text-6xl font-weight-bold;
  }

  .heading-secondary {
    @apply font-nevermind-bold text-4xl md:text-5xl font-weight-bold;
  }

  .heading-tertiary {
    @apply font-nevermind-bold text-2xl md:text-3xl font-weight-bold;
  }

  .heading-quaternary {
    @apply font-nevermind-bold text-xl md:text-2xl font-weight-bold;
  }

  .text-body {
    @apply font-poppins text-base font-weight-normal text-black;
  }

  .text-body-lg {
    @apply font-poppins text-lg font-weight-normal text-black;
  }

  .text-body-sm {
    @apply font-poppins text-sm font-weight-normal text-brand-text-gray;
  }

  .text-subheading {
    @apply font-poppins text-lg font-weight-medium text-brand-text-gray;
  }

  .text-caption {
    @apply font-poppins text-sm font-weight-normal text-gray-600;
  }

  .text-label {
    @apply font-poppins text-base font-weight-medium text-gray-700;
  }

  /* Button Components - Using Global Font System */
  .btn-primary {
    @apply bg-primary-400 text-white font-nevermind-bold text-lg font-weight-bold px-6 py-3 rounded-xl
           shadow-button-inset border-none cursor-pointer transition-transform duration-200
           hover:-translate-y-1;
  }

  .btn-secondary {
    @apply bg-white text-brand-purple-dark font-nevermind-bold text-lg font-weight-bold px-6 py-3 rounded-xl
           border border-gray-300 cursor-pointer transition-all duration-200
           hover:bg-gray-50 hover:shadow-card-hover;
  }

  .btn-outline {
    @apply bg-transparent text-black font-nevermind-bold text-lg font-weight-bold px-6 py-3 rounded-xl
           border border-black cursor-pointer transition-all duration-200
           hover:bg-black hover:text-white;
  }

  .btn-small {
    @apply font-nevermind-bold text-sm font-weight-bold px-4 py-2 rounded-lg
           cursor-pointer transition-all duration-200;
  }

  .btn-large {
    @apply font-nevermind-bold text-xl font-weight-bold px-8 py-4 rounded-xl
           cursor-pointer transition-all duration-200;
  }

  .btn-cta {
    @apply mt-4 bg-primary-400 text-3xl font-medium text-white rounded-2xl py-4 px-12
           border-none shadow-button cursor-pointer font-poppins transition-transform duration-200
           hover:-translate-y-1 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:translate-y-0
           max-[1023px]:text-[1.75rem] max-[767px]:text-[1.3rem] max-[767px]:rounded-[0.6rem]
           max-[767px]:py-2 max-[767px]:px-8;
  }

  /* Card Components */
  .card-base {
    @apply bg-white rounded-2xl shadow-card border border-gray-200 p-6;
  }

  .card-hover {
    @apply card-base transition-all duration-300 hover:shadow-card-hover hover:scale-105;
  }

  .card-game {
    @apply card-hover text-center;
  }

  .card-pricing {
    @apply card-hover flex flex-col items-center justify-between min-w-[180px] w-80 h-80
           md:w-64 md:h-72 lg:w-72 lg:h-80;
  }

  /* Form Components - Using Global Font System */
  .form-input {
    @apply w-full py-3 px-4 border border-gray-300 rounded-xl font-poppins text-base font-weight-normal
           transition-colors duration-200 focus:outline-none focus:border-primary-400
           placeholder:text-gray-500;
  }

  .form-input-error {
    @apply form-input border-red-500 text-red-500;
  }

  .form-label {
    @apply text-left text-gray-600 font-poppins text-base font-weight-medium mb-2;
  }

  .form-helper-text {
    @apply font-poppins text-sm font-weight-normal text-gray-500 mt-1;
  }

  .form-error-text {
    @apply font-poppins text-sm font-weight-normal text-red-500 mt-1;
  }

  /* Section Components */
  .section-padding {
    @apply py-16 px-8 md:py-20 md:px-12;
  }

  .section-heading {
    @apply text-center mb-8 md:mb-12;
  }

  /* Grid Components */
  .grid-auto-fit {
    @apply grid justify-items-center gap-8;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  }

  .grid-cards {
    @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6;
  }

  /* Responsive Text */
  .text-responsive-lg {
    @apply text-lg md:text-xl lg:text-2xl;
  }

  .text-responsive-xl {
    @apply text-xl md:text-2xl lg:text-3xl;
  }

  /* About Cards - Using Global Font System */
  .about-card {
    @apply w-80 text-center font-poppins font-weight-medium text-brand-purple-dark
           md:w-72;
  }

  .about-card-image {
    @apply md:w-72 md:h-52;
  }

  .about-card-text {
    @apply text-label;
  }

  /* Award Components */
  .awards-grid {
    @apply flex justify-center items-center gap-8 flex-wrap max-w-[90%] mx-auto;
  }

  .award-image {
    @apply w-[150px] h-[150px] object-contain;
  }

  .awards-grid-home {
    @apply grid grid-cols-2 gap-5 lg:grid-cols-7 lg:items-center;
  }

  .award-image-home {
    @apply md:w-20 md:h-20;
  }

  /* Review Components */
  .review-card {
    @apply pt-4 pr-0 pb-0 pl-0 rounded-lg bg-brand-bg-light flex flex-col items-center justify-between text-brand-purple-dark;
  }

  .review-content {
    @apply text-body my-4 mx-8 mb-2;
  }

  .review-footer {
    @apply bg-white rounded-b-lg border-t border-[#e0e0e0] pt-4 mt-4;
  }

  .review-author {
    @apply text-body-sm text-[#666] m-0;
  }



  /* Grid Components */
  .grid-cards {
    @apply grid gap-[10px] w-full;
  }

  /* Section Components */
  .section-container {
    @apply max-w-container mx-auto;
  }

  .section-heading {
    @apply text-center mb-8;
  }

  /* Filter Components */
  .btn-filter {
    @apply rounded-[40px] border border-[#d2d2d2] text-[#737373] py-2 px-4 whitespace-nowrap cursor-pointer
           hover:bg-[#fc8800] hover:text-white hover:border-transparent transition-colors duration-200;
  }

  .btn-filter-active {
    @apply bg-[#fc8800] text-white border-transparent;
  }

  /* Product Card Components */
  .card-product {
    @apply card-hover max-w-[350px] flex flex-col justify-between;
  }

  .skill-tag {
    @apply text-caption bg-[#f7f7f7] py-2 px-4 rounded-[2.5rem] border border-[#e9e9e9];
  }

  /* Article Components */
  .article-card {
    @apply flex border-b border-[#898989] py-4 gap-[10px] cursor-pointer flex-col md:flex-row md:gap-[30px] md:py-6;
  }

  .article-image {
    @apply flex items-center md:w-[35%];
  }

  .article-content {
    @apply w-full flex flex-col relative md:w-[65%];
  }

  .article-date {
    @apply text-[#b7b7b7] text-caption my-0 mb-7;
  }

  .article-title {
    @apply my-0 mb-2 heading-tertiary;
  }

  .article-excerpt {
    @apply m-0 text-[#747474] text-body-sm pb-10;
  }

  .article-read-more {
    @apply text-[#0169dd] text-body-sm absolute m-0 bottom-0 right-0;
  }

  /* Form Components */
  .form-input-underline {
    @apply h-[52px] border-none bg-transparent border-b border-black py-0 px-4 text-body text-[#5b5b5b] w-full my-4 mx-0
           focus:outline-none placeholder:text-body placeholder:text-[#5b5b5b] box-border;
  }

  .form-textarea {
    @apply bg-transparent border border-black py-4 px-4 text-body text-[#5b5b5b] w-full my-8 mx-0 rounded-lg box-border
           focus:outline-none placeholder:text-body placeholder:text-[#5b5b5b];
  }

  /* Get Started Components */
  .get-started-container {
    @apply max-w-[480px] md:w-full mx-auto text-center bg-white py-0 pr-0 pb-2 pl-0 box-border;
  }

  /* Login Components */
  .login-container {
    @apply w-full max-w-[480px] mx-auto py-0 px-[2vw] pb-[6vh] flex flex-col items-center bg-white
           shadow-[0_0.5vmin_2vmin_rgba(0,0,0,0.1)] max-[480px]:rounded-none max-[480px]:h-full;
  }

  .form-input-login {
    @apply w-full py-[2vh] px-[3vw] border-[0.2vmin] border-solid border-[#e0e0e0] rounded-[2vmin]
           text-body transition-[border-color] duration-200 box-border placeholder:text-[#6c757d];
  }

  .password-toggle-btn {
    @apply absolute right-[3vw] top-1/2 transform -translate-y-1/2 bg-none border-none cursor-pointer
           py-[1vh] px-0 text-[clamp(1rem,2.5vw,1.25rem)];
  }

  .form-error-message {
    @apply bg-[#f8d7ce] text-[#dd4a38] my-[1vh] mx-0 rounded-[1vmin] py-[1vh] px-[2vw]
           text-caption flex items-center;
  }

  /* User Home Screen Components */
  .user-home-screen {
    @apply bg-cover bg-center bg-no-repeat min-h-[90vh] w-full overflow-hidden box-border
           container-main relative transition-all duration-300 ease-in-out;
  }

  .user-home-bg {
    @apply absolute top-0 left-0 w-full h-full object-cover transition-opacity duration-300 ease-in-out;
  }

  .user-home-header {
    @apply flex items-center gap-[10px] p-4 px-8 justify-between text-subheading;
  }

  .user-greeting {
    @apply flex items-center gap-[10px];
  }

  .user-avatar {
    @apply h-20 w-20 object-cover;
  }

  .user-greeting-text {
    @apply text-subheading;
  }

  .theme-toggle {
    @apply cursor-pointer relative w-[115px] h-[65px] overflow-hidden;
  }

  .theme-toggle-image {
    @apply absolute top-0 left-0 transition-opacity duration-300 ease-in-out cursor-pointer;
  }

  .games-container {
    @apply overflow-x-auto overflow-y-hidden flex items-center p-4 scrollbar-none;
  }

  .hero-game {
    @apply relative cursor-pointer;
  }

  .hero-game-title {
    @apply absolute text-subheading top-3 left-[38%] text-white;
  }

  .hero-play-btn {
    @apply absolute -bottom-[30px] left-1/2 transform -translate-x-1/2 z-10 animate-pulse;
  }

  .game-tiles-row {
    @apply my-4 flex gap-5;
  }

  .game-tiles-row-offset {
    @apply pl-[2.3rem];
  }

  .game-tile {
    @apply w-[290px] h-[200px] transform skew-x-[10deg] relative rounded-[14px] overflow-hidden
           shadow-[0px_6.13px_0px_0px_#4a2d99] bg-gradient-to-br from-[#c3b1ff] to-[#7a4bf8]
           flex-shrink-0 box-border cursor-pointer;
  }

  .game-tile-overlay {
    @apply absolute text-white bottom-0 p-0 px-[0.63rem] overflow-hidden rounded-bl-[10px]
           left-0 w-full bg-gradient-to-t from-black to-transparent h-10 flex items-center box-border;
  }

  .game-tile-title {
    @apply block transform -skew-x-[10deg] text-label;
  }

  /* Profile Page Components */
  .profile-page {
    @apply min-h-screen bg-white mt-8;
  }

  .profile-container {
    @apply mx-auto flex flex-col;
  }

  .subscription-card {
    @apply bg-white rounded-lg shadow-[0_1px_3px_0_rgba(0,0,0,0.1),0_1px_2px_0_rgba(0,0,0,0.06)]
           mb-6 p-8 w-[818px] bg-[radial-gradient(95.78%_61.92%_at_50%_35.25%,#cbafff_0%,#9258fe_100%)]
           text-white flex flex-col items-center justify-between max-w-[765px] self-center p-4 text-center overflow-hidden;
  }

  .subscription-content {
    @apply flex flex-col items-center text-left gap-4 pl-4;
  }

  .subscription-text {
    @apply text-subheading text-[#e2e2e2] leading-8 break-words;
  }

  .subscription-upgrade-btn {
    @apply bg-white text-[#8b5cf6] shadow-[0px_5.35px_0px_0px_#4a2d7f] w-auto h-auto py-[6.4px] px-[32.11px]
           rounded-[10.7px] border-none cursor-pointer;
  }

  .subscription-image {
    @apply pr-14 w-[210px] h-[173px];
  }

  .profile-section {
    @apply text-center;
  }

  .profile-header {
    @apply mb-4;
  }

  .profile-header-content {
    @apply flex justify-between items-center;
  }

  .profile-actions {
    @apply flex gap-4;
  }

  .profile-action-btn {
    @apply flex items-center gap-2;
  }

  .profile-email {
    @apply text-body-lg mb-6;
  }

  .membership-info-content {
    @apply bg-white rounded-lg shadow-lg p-6 mb-6;
  }

  .membership-info {
    @apply flex justify-between items-center py-3;
  }

  .membership-label {
    @apply text-label;
  }

  .membership-status {
    @apply text-label;
  }

  .membership-active {
    @apply text-green-600;
  }

  .membership-inactive {
    @apply text-red-600;
  }

  .membership-date {
    @apply text-body pr-4;
  }

  .membership-divider {
    @apply border-t border-[#B9B9B9];
  }

  .cancel-subscription-section {
    @apply text-left;
  }

  .cancel-subscription-btn {
    @apply pl-0 pt-0;
  }

  .cancel-status-success {
    @apply text-green-600 mt-2;
  }

  .cancel-status-error {
    @apply text-red-600 mt-2;
  }

  .download-section {
    @apply mb-6 text-center;
  }

  .qr-codes {
    @apply flex justify-center gap-8 flex-wrap;
  }

  .qr-code-item {
    @apply flex flex-col items-center;
  }

  .qr-code {
    @apply rounded-lg;
  }

  .qr-code-label {
    @apply text-body text-center mt-2;
  }

  .explore-button {
    @apply w-full max-w-md mx-auto;
  }

  /* Purchase Page Components */
  .purchase-page {
    @apply min-h-screen bg-white text-sm md:text-xs;
  }

  .purchase-header {
    @apply relative w-full h-[15.625rem] overflow-hidden;
  }

  .purchase-banner {
    @apply w-full h-full object-cover;
  }

  .purchase-header-content {
    @apply absolute -top-24 left-0 right-0 bottom-0 flex flex-col justify-center items-center;
  }

  .purchase-features {
    @apply flex flex-col gap-4;
    place-items: flex-start;
  }

  .purchase-feature {
    @apply flex items-center gap-2 justify-center;
  }

  .purchase-main {
    @apply max-w-[33.125rem] -mt-8 mx-auto text-center px-4 mb-8;
  }

  .purchase-content {
    @apply p-8;
  }

  .plan-card {
    @apply border-[0.5rem] border-[#eaeaea] rounded-[0.938rem] p-6 mb-6 cursor-pointer relative;
  }

  .plan-card-best-value {
    @apply border-[0.5rem] border-[#eaeaea] cursor-pointer relative;
  }

  .plan-card-selected {
    @apply border-[0.5rem] border-[#ffdf00] cursor-pointer;
  }

  .plan-best-value-badge {
    @apply absolute -top-6 right-2 flex items-center justify-center z-10;
  }

  .plan-best-value-text {
    @apply absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2
           text-caption text-white font-bold whitespace-nowrap pointer-events-none;
  }

  .plan-header {
    @apply bg-[#eaeaea] -mx-6 -mt-6 mb-4 px-4 py-4 rounded-t-[0.813rem] flex justify-between items-center;
  }

  .plan-header-selected {
    @apply bg-[#ffdf00];
  }

  .plan-name {
    @apply text-label;
  }

  .plan-badge {
    @apply bg-[#5a54af] text-white py-2 px-5 rounded-[0.938rem] text-caption;
  }

  .plan-pricing {
    @apply flex flex-col justify-center items-center;
  }

  .plan-original-price {
    @apply line-through text-[#1a1713] text-subheading mr-2;
    text-decoration: line-through;
  }

  .plan-price {
    @apply heading-primary text-[#1a1713];
  }

  .plan-offer-badge {
    @apply bg-[#306f5b] text-white inline-block py-2 px-4 rounded-[1.25rem] my-2 text-label;
  }

  .plan-description {
    @apply text-[#6c757d] text-label text-center m-0;
  }

  .plan-description-best-value {
    @apply text-black;
  }

  /* OTP Components */
  .otp-resend-btn {
    @apply border-none py-[1vh] px-[2vw] text-body rounded-[1vmin];
  }

  .otp-resend-disabled {
    @apply bg-[#ddd] text-[#999] cursor-not-allowed;
  }

  .otp-resend-active {
    @apply bg-[#f1f1f1] text-black cursor-pointer transition-[background-color,color] duration-200;
  }

  .otp-resend-success {
    @apply bg-[#4caf50] text-white cursor-pointer;
  }

  .otp-cta-button {
    @apply w-full;
  }

  .otp-cta-step1 {
    margin-top: 36vh !important;
  }

  .otp-cta-step1-error {
    margin-top: 26vh !important;
  }

  .otp-cta-step2 {
    margin-top: 18vh !important;
  }

  .otp-cta-step2-error {
    margin-top: 6vh !important;
  }

  /* Navbar Components */
  .navbar {
    @apply flex justify-between items-center p-3 bg-white border-b border-[#dbdbdb]
           sticky top-0 z-[100] font-poppins font-medium w-full box-border;
  }

  .nav-link {
    @apply text-[#666666] no-underline py-2 px-2 block relative whitespace-nowrap
           transition-colors duration-300 ease-in-out
           after:content-[''] after:absolute after:bottom-0 after:left-1/2 after:transform
           after:-translate-x-1/2 after:w-0 after:h-1 after:bg-primary-400 after:rounded-sm
           after:transition-all after:duration-300 after:ease-in-out
           hover:text-black hover:after:w-full;
  }

  .nav-link.active {
    @apply text-black after:w-full;
  }

  .nav-menu {
    @apply flex list-none m-0 p-0 items-center flex-nowrap;
  }

  .nav-item {
    @apply ml-4 whitespace-nowrap;
  }

  .nav-hamburger {
    @apply hidden cursor-pointer;
  }

  .nav-hamburger-bar {
    @apply w-[30px] h-[3px] bg-black my-1 transition-all duration-[0.4s];
  }

  .nav-hamburger.active .nav-hamburger-bar:nth-child(1) {
    @apply rotate-[-45deg] translate-x-[-5px] translate-y-[6px];
  }

  .nav-hamburger.active .nav-hamburger-bar:nth-child(2) {
    @apply opacity-0;
  }

  .nav-hamburger.active .nav-hamburger-bar:nth-child(3) {
    @apply rotate-[45deg] translate-x-[-5px] translate-y-[-6px];
  }

  .nav-free-trial-btn {
    @apply font-semibold text-2xl bg-primary-400 text-white rounded-lg shadow-button
           py-2 px-6 whitespace-nowrap transition-colors duration-300 hover:text-white;
  }

  /* Mobile Navbar Styles */
  @media (max-width: 820px) {
    .navbar {
      @apply px-3 py-3 max-w-full overflow-x-hidden;
    }

    .nav-hamburger {
      @apply block;
    }

    .nav-menu {
      @apply flex-col w-full h-screen fixed top-0 bg-white items-center
             transition-all duration-300 overflow-y-auto overflow-x-hidden p-4 box-border;
    }

    .nav-menu.closed {
      @apply right-[-150%];
    }

    .nav-menu.open {
      @apply right-0 z-20;
    }

    .nav-item {
      @apply my-4 text-2xl text-center ml-0;
    }

    .nav-link {
      @apply text-xl p-2;
    }

    .nav-free-trial-btn {
      @apply text-xl inline-block text-center;
    }
  }

  /* Footer Components */
  .footer-wrapper {
    @apply max-w-[1728px] mx-auto;
  }

  .pink-footer-wrapper {
    @apply bg-[url('/images/pinkBg.webp')] bg-cover bg-no-repeat bg-center w-full pt-[8.3rem] pb-6;
  }

  .lock-content-wrapper {
    @apply flex items-center flex-col text-[2.5rem] md:text-[5rem];
  }

  .lock-content-wrapper p:first-child {
    @apply m-0;
  }

  .lock-content-wrapper p:nth-child(2) {
    @apply text-white my-4 mx-0 md:mb-8;
  }

  .footer-cta-button {
    @apply mt-0 bg-primary-400 text-2xl text-white rounded-2xl py-3 px-8 no-underline border-none
           shadow-button cursor-pointer font-poppins font-medium
           md:text-[2rem] md:py-3 md:px-12;
  }

  .game-cards-wrapper {
    @apply flex gap-[10px] justify-center text-center mt-8 mx-8 mb-0 md:gap-5;
  }

  .game-cards-section {
    @apply flex gap-[10px] flex-wrap;
  }

  .game-cards-section:first-child {
    @apply justify-end gap-5;
  }

  .game-cards-section:last-child {
    @apply justify-start gap-5;
  }

  .game-card-col1 div {
    @apply bg-white p-[5px] rounded-xl md:p-[3px];
  }

  .game-card-col1 p {
    @apply m-0 text-xs md:text-base;
  }

  .game-card-col2 {
    @apply flex flex-col justify-between gap-[15px];
  }

  .game-card-col2 div {
    @apply bg-white p-[5px] rounded-xl md:p-[3px];
  }

  .game-card-col2 p {
    @apply m-0 text-xs md:text-base;
  }

  .footer-green-wrapper {
    @apply relative bg-[url('/images/footer.webp')] bg-cover bg-no-repeat bg-top w-full
           min-h-[1250px] flex items-end
           md:min-h-[1050px]
           lg:min-h-[830px]
           xl:min-h-[1200px];
  }

  .footer-content-wrapper {
    @apply flex justify-center items-center absolute w-[98%] left-1/2
           transform -translate-x-1/2 text-white rounded-xl flex-col bottom-[10px]
           md:flex-row;
    background-color: rgba(255, 255, 255, 0.12);
  }

  .footer-content-wrapper > div {
    @apply w-full;
  }

  .footer-content-left {
    @apply flex flex-col gap-[13px] p-0 md:gap-[90px] md:pl-8;
  }

  .footer-content-left > div {
    @apply w-4/5 ml-2;
  }

  .footer-content-left-img {
    @apply flex gap-[5px];
  }

  .footer-content-right {
    @apply flex flex-col border-l-0 md:border-l;
    border-color: rgba(255, 255, 255, 0.3);
  }

  .footer-content-right-top {
    @apply flex w-full text-left border-b border-t mt-8 flex-col p-4 box-border
           md:border-t-0 md:mt-0 md:flex-row md:p-0;
    border-color: rgba(255, 255, 255, 0.3);
  }

  .footer-content-right-top > div {
    @apply w-full ml-[0.3rem] md:w-1/3 md:ml-4;
  }

  .footer-content-right-top > div:nth-child(1),
  .footer-content-right-top > div:nth-child(2) {
    @apply md:border-r;
    border-color: rgba(255, 255, 255, 0.3);
  }

  .footer-content-right-top ul {
    @apply list-none text-left p-0 m-0 text-xl font-poppins;
  }

  .footer-content-right-top li {
    @apply my-2;
    color: rgba(255, 255, 255, 0.7);
  }

  .footer-link-hoverable:hover {
    @apply inline my-2 text-white border-b border-white box-border;
  }

  .store-icons-wrapper {
    @apply grid gap-4 grid-cols-4 absolute bottom-[280px] left-1/2 transform -translate-x-1/2
           max-[700px]:grid-cols-2 max-[700px]:bottom-[680px];
  }

  .store-icons {
    @apply cursor-pointer hover:scale-105 transition-transform duration-300
           max-[700px]:w-[178px] max-[700px]:h-[50px]
           md:max-lg:w-[140px] md:max-lg:h-[50px];
  }

  .footer-content-wrapper > div:first-child {
    @apply md:w-2/5;
  }

  /* Media Mentions Components */
  .media-mentions-container {
    @apply flex gap-5 my-0 overflow-x-auto scrollbar-none py-8 pr-0 pb-12 pl-4
           md:py-8 md:pr-0 md:pb-12 md:pl-12;
  }

  .media-mention-card {
    @apply w-[300px] bg-[#f9f9f9] cursor-pointer flex-shrink-0 border border-[#c8c8c8]
           rounded-2xl transition-all duration-300 relative;
  }

  .media-mention-image {
    @apply w-full h-[315px] bg-cover bg-center bg-no-repeat rounded-t-2xl flex justify-center items-center
           relative overflow-hidden before:content-[''] before:absolute before:top-0 before:left-0 before:right-0
           before:bottom-0 before:bg-black/0 before:transition-colors before:duration-300 before:z-[1]
           before:rounded-t-2xl;
  }

  .media-mention-link {
    @apply inline p-[0.8rem] bg-[#f9f9f9] rounded-2xl relative z-[2] top-[200px]
           transition-all duration-300 ease-in-out flex items-center gap-[5px]
           no-underline text-inherit;
  }

  .media-mention-description {
    @apply p-0 px-2 box-border text-body;
  }

  /* Partnership Components */
  .partnership-header {
    @apply flex justify-center items-center py-8 px-4 gap-5 flex-col md:flex-row;
  }

  .partnership-content {
    @apply md:px-12 md:my-auto md:block md:my-auto md:text-left;
  }

  .partnership-cta-btn {
    @apply mr-20 md:m-0 md:py-[0.8rem] md:px-8;
  }

  .partnership-cta-link {
    @apply text-white no-underline;
  }

  .partnership-image {
    @apply w-[320px] h-[320px];
  }

  .partnership-partners {
    @apply text-center py-8 px-4 md:p-12;
  }

  .partners-logos {
    @apply flex flex-col items-center gap-8 md:flex-row md:justify-center;
  }

  .partnership-collaboration {
    @apply text-center py-8 px-4;
  }

  .collaboration-options {
    @apply flex justify-around items-center gap-5 flex-wrap;
  }

  .collaboration-option {
    @apply flex flex-col items-center;
  }

  .collaboration-icon {
    @apply bg-[#e0edff] p-14;
  }

  .collaboration-text {
    @apply text-center text-body;
  }

  /* Support Components */
  .support-header {
    @apply max-w-full bg-[url('/images/contactUs/contactUsBanner.webp')] bg-cover bg-center bg-no-repeat
           flex object-cover flex-col md:flex-row;
  }

  .support-content {
    @apply flex w-[95%] md:w-1/2 justify-center flex-col mx-auto md:px-12 md:my-auto;
  }

  .support-contact-info {
    @apply flex flex-col;
  }

  .support-contact-label {
    @apply font-bold text-subheading my-4 mx-0 mb-[-0.8rem];
  }

  .support-image {
    @apply flex w-[95%] md:w-1/2 justify-center flex-col items-center;
  }

  .support-faq {
    @apply m-4 md:m-12;
  }

  .support-form-section {
    @apply bg-[#f9f9f9] p-4 md:p-12 w-full mx-auto box-border;
  }

  .support-form {
    @apply space-y-4;
  }

  .support-attachment {
    @apply flex items-center gap-4 my-4;
  }

  .support-attachment-icon {
    @apply text-[1.5rem] cursor-pointer text-[#9258fe] hover:text-[#7a4bf8];
  }

  .support-attachment-name {
    @apply text-[#5b5b5b] text-sm;
  }

  .support-submit-btn {
    @apply mt-8 mr-20;
  }

  .support-links {
    @apply p-4 md:p-12;
  }

  .support-link-item {
    @apply flex items-center justify-between py-4 border-b border-[#e0e0e0] cursor-pointer
           hover:bg-[#f5f5f5] transition-colors;
  }

  /* 404 Not Found Components */
  .not-found-container {
    @apply container-main w-full h-full bg-cover bg-center bg-repeat pt-5 text-white
           flex justify-center items-center flex-col text-center;
    background-image: url('data:image/svg+xml;utf8,%3Csvg%20viewBox%3D%220%200%202000%201400%22%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%3E%3Cdefs%3E%3Cfilter%20id%3D%22b%22%20x%3D%22-200%25%22%20y%3D%22-200%25%22%20width%3D%22500%25%22%20height%3D%22500%25%22%3E%3CfeGaussianBlur%20in%3D%22SourceGraphic%22%20stdDeviation%3D%2220%22/%3E%3C/filter%3E%3C/defs%3E%3Cpath%20fill%3D%22%23000336%22%20d%3D%22M0%200h2000v1400H0z%22/%3E%3C/svg%3E');
  }

  .not-found-title {
    @apply font-black text-[5rem] m-0 md:text-[3rem] max-[480px]:text-[2.5rem];
  }

  .not-found-message {
    @apply text-subheading my-[10px] mx-0 md:text-subheading max-[480px]:text-caption;
  }

  .not-found-button {
    @apply py-[15px] px-[90px] rounded-lg text-subheading cursor-pointer bg-transparent text-white
           border border-white md:px-[70px] max-[480px]:text-caption max-[480px]:py-[10px] max-[480px]:px-[40px];
  }

  .not-found-image {
    @apply mt-[60px] h-[300px] w-[300px] max-[480px]:w-[284px] max-[480px]:h-[244px] max-[480px]:mt-[100px];
  }

  /* Blog/Article Detail Components */
  .blog-detail-container {
    @apply container-main my-0 mx-auto p-2 md:p-12;
  }

  .blog-detail-image {
    @apply flex justify-center items-center w-full bg-[#f9f9f9] mb-8;
  }

  .blog-detail-meta {
    @apply flex gap-[1px] m-0 text-caption justify-between md:text-body md:gap-[5px] md:justify-start;
  }
}

@layer utilities {
  .noLinkStyle {
    text-decoration: none;
    color: inherit;
  }

  .noScroll {
    overflow: hidden;
  }

  .scrollbar-none {
    scrollbar-width: none;
    -ms-overflow-style: none;
  }

  .scrollbar-none::-webkit-scrollbar {
    display: none;
  }
}

/* Custom scrollbar styles */
.scrollbar-none {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.scrollbar-none::-webkit-scrollbar {
  display: none;
}

/* Custom checkbox styles for EnterEmailSection */
input[type="checkbox"] + label::before {
  content: "";
  position: absolute;
  left: 3px;
  top: 0px;
  width: 22px;
  height: 22px;
  border: 2px solid #ccc;
  border-radius: 4px;
  background-color: white;
  box-sizing: border-box;
}

.checkbox-error input[type="checkbox"] + label::before {
  border: 2px solid red;
}

input[type="checkbox"]:checked + label::before {
  border: none;
  background-color: white;
}

input[type="checkbox"]:checked + label::after {
  content: "";
  position: absolute;
  left: 0px;
  top: -5px;
  width: 30px;
  height: 30px;
  background-image: url("data:image/svg+xml,%3Csvg width='31' height='30' viewBox='0 0 31 30' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M13.7615 20.3697L21.4671 12.6641C21.9362 12.195 21.9362 11.4344 21.4671 10.9653C20.9979 10.4961 20.2373 10.4961 19.7682 10.9653L13.7615 16.972L11.1525 14.363C10.6834 13.8939 9.92277 13.8939 9.45364 14.363C8.98451 14.8321 8.98451 15.5927 9.45364 16.0619L13.7615 20.3697ZM6.96602 26.1944C6.2986 26.1944 5.72726 25.9567 5.25198 25.4815C4.7767 25.0062 4.53906 24.4348 4.53906 23.7674V6.77876C4.53906 6.11135 4.7767 5.54 5.25198 5.06472C5.72726 4.58945 6.2986 4.35181 6.96602 4.35181H23.9547C24.6221 4.35181 25.1934 4.58945 25.6687 5.06472C26.144 5.54 26.3816 6.11135 26.3816 6.77876V23.7674C26.3816 24.4348 26.144 25.0062 25.6687 25.4815C25.1934 25.9567 24.6221 26.1944 23.9547 26.1944H6.96602ZM6.96602 23.7674H23.9547V6.77876H6.96602V23.7674Z' fill='%231DC368'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}
