/* Tailwind CSS Directives */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Import Modular CSS Files */
@import '../styles/base.css';
@import '../styles/typography.css';
@import '../styles/buttons.css';
@import '../styles/forms.css';
@import '../styles/layout.css';
@import '../styles/navigation.css';
@import '../styles/footer.css';
@import '../styles/pages.css';
@import '../styles/utilities.css';

/*
 * All component styles have been moved to modular CSS files in src/styles/
 * This approach provides better organization, maintainability, and performance
 *
 * File Structure:
 * - base.css: Core typography, fonts, and global settings
 * - typography.css: Text styles and headings
 * - buttons.css: Button components and variants
 * - forms.css: Form inputs and validation styles
 * - layout.css: Layout components and grids
 * - navigation.css: Navbar and navigation components
 * - footer.css: Footer components and styles
 * - pages.css: Page-specific components
 * - utilities.css: Utility classes and animations
 *
 * Benefits:
 * - Reduced bundle size (from 1277 lines to ~35 lines)
 * - Better organization and maintainability
 * - Easier to find and modify specific styles
 * - Improved performance through better caching
 * - Modular architecture for scalability
 */