"use client";
import { useAuth } from "@/context/AuthContext";
import apiClient from "@/utils/axiosUtil";
import { faArrowRight } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { useTranslations } from "next-intl";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import vector from "../../../public/images/login-daskbox/LogoutLogo.png";
import appstoreqr from "../../../public/images/login-daskbox/appstoreqr.png";
import daskbox from "../../../public/images/login-daskbox/daskboxImage.webp";
import playstoreqr from "../../../public/images/login-daskbox/playstoreqr.png";
import vectord from "../../../public/images/login-daskbox/supportLogo.png";

const ProfilePage = () => {
  const [userDetails, setUserDetails] = useState({});
  const [isLoading, setIsLoading] = useState(false);
  const [cancelStatus, setCancelStatus] = useState({ success: false, error: null });

  useEffect(() => {
    const storedUserDetails = JSON.parse(localStorage.getItem("UserDetails")) || {};
    setUserDetails(storedUserDetails);
  }, []);
  const { email, expiryDate, StripeId } = userDetails;
  const date = new Date(expiryDate);
  const router = useRouter();
  const t = useTranslations("Dashboard");
  const { logout, isSubscribed } = useAuth();

  const logoutHandler = () => {
    router.replace("/login");
    logout();
  };

  const handleCancelSubscription = async () => {
    if (!confirm("Are you sure you want to cancel your subscription?")) {
      return;
    }

    setIsLoading(true);
    setCancelStatus({ success: false, error: null });

    try {
      const response = await apiClient.get(
        `${process.env.NEXT_PUBLIC_SUBSSERVICE_BASE_URL}/subscription/cancel`
      );

      if (response.status === 200) {
        setCancelStatus({ success: true, error: null });

        // Update user details in localStorage
        const updatedUserDetails = { ...userDetails, isSubscribed: false };
        localStorage.setItem("UserDetails", JSON.stringify(updatedUserDetails));
        setUserDetails(updatedUserDetails);

        // Update auth context
        logout();
        setTimeout(() => {
          router.replace("/login");
        }, 2000);
      }
    } catch (error) {
      console.error("Error cancelling subscription:", error);
      setCancelStatus({
        success: false,
        error:
          error.response?.data?.message || "Failed to cancel subscription. Please try again later.",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="profile-page">
      <div className="profile-container">
        <div className="subscription-card">
          <div className="subscription-content">
            {isSubscribed && (
              <p className="subscription-text">
                {t("DashboardPlanLine1")} <br />
                {t("DashboardPlanLine2")}
                <br />
                {t("DashboardPlanLine3")}
              </p>
            )}
            {!isSubscribed && (
              <p className="subscription-text">
                {t("DashboardPlanLine4")} <br />
                {t("DashboardPlanLine5")}
                <br />
                <button
                  className="btn-primary subscription-upgrade-btn"
                  onClick={() => {
                    router.push("/acquisition");
                  }}
                >
                  {t("DashboardAnnual")}
                </button>
              </p>
            )}
          </div>
          <Image
            src={daskbox}
            alt="Subscription illustration"
            className="subscription-image"
            width={210}
            height={173}
          />
        </div>

        <div className="profile-section">
          <div className="profile-header">
            <div className="profile-header-content">
              <h1 className="heading-secondary"> {t("DashboardInformation")}</h1>
              <div className="profile-actions">
                <button
                  className="btn-secondary profile-action-btn"
                  onClick={() => (window.location.href = "https://support.skidos.com/")}
                >
                  {t("DashboardSupport")}{" "}
                  <Image
                    style={{ marginLeft: "0.325rem" }}
                    src={vectord}
                    alt="Support"
                    width={13}
                    height={13}
                  />
                </button>
                <button className="btn-secondary profile-action-btn" onClick={logoutHandler}>
                  {t("DashboardLogout")}{" "}
                  <Image
                    style={{ marginLeft: "0.325rem" }}
                    src={vector}
                    alt="Logout"
                    width={13}
                    height={13}
                  />
                </button>
              </div>
            </div>
          </div>

          <p className="profile-email">{email}</p>
          <div className="profile-header">
            <div className="profile-header-content">
              <h1 className="heading-tertiary"> {t("DashboardMembership")}</h1>
            </div>
          </div>
          <div className="membership-info-content">
            <div className="membership-info">
              <span className="membership-label"> {t("DashboardSubscript")}</span>
              <span
                className={`membership-status ${!isSubscribed ? "membership-inactive" : "membership-active"}`}
              >
                {isSubscribed ? t("DashboardActive") : t("DashboardInactive")}
              </span>
            </div>
            <div className="membership-divider"></div>
            {isSubscribed && (
              <div className="membership-info">
                <span className="membership-label">{t("DashboardExpire")}</span>
                <span className="membership-date">
                  {date.toLocaleDateString("en-GB", {
                    day: "numeric",
                    month: "long",
                    year: "numeric",
                  })}
                </span>
              </div>
            )}

            {isSubscribed && StripeId && (
              <div className="cancel-subscription-section">
                <button
                  className="btn-secondary cancel-subscription-btn"
                  onClick={handleCancelSubscription}
                  disabled={isLoading}
                >
                  {isLoading ? "Processing..." : t("DashboardCancelSubscription")}
                  <FontAwesomeIcon style={{ marginLeft: "0.325rem" }} icon={faArrowRight} />
                </button>

                {cancelStatus.success && (
                  <p className="cancel-status-success">
                    Your subscription has been successfully cancelled. You will be redirected
                    shortly.
                  </p>
                )}

                {cancelStatus.error && <p className="cancel-status-error">{cancelStatus.error}</p>}
              </div>
            )}
          </div>
        </div>

        <div className="download-section">
          <div className="profile-header">
            <div className="profile-header-content">
              <h1 className="heading-tertiary">{t("DashboardDownload")}</h1>
            </div>
          </div>
          <div className="qr-codes">
            <div className="qr-code-item">
              <Image
                src={appstoreqr}
                alt="App Store QR Code"
                className="qr-code"
                width={200}
                height={200}
              />
              <p className="qr-code-label">{t("DashboardApp")}</p>
            </div>
            <div className="qr-code-item">
              <Image
                src={playstoreqr}
                alt="Play Store QR Code"
                className="qr-code"
                width={200}
                height={200}
              />
              <p className="qr-code-label">{t("DashboardPlay")}</p>
            </div>
          </div>
        </div>

        <button
          className="btn-primary explore-button"
          onClick={() => {
            router.push(isSubscribed ? "/user-home-screen" : "/products");
          }}
        >
          {t("DashboardExplore")}
        </button>
      </div>
    </div>
  );
};
export default ProfilePage;
