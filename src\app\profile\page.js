"use client";
import { useAuth } from "@/context/AuthContext";
import apiClient from "@/utils/axiosUtil";
import { faArrowRight } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { useTranslations } from "next-intl";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import vector from "../../../public/images/login-daskbox/LogoutLogo.png";
import appstoreqr from "../../../public/images/login-daskbox/appstoreqr.png";
import daskbox from "../../../public/images/login-daskbox/daskboxImage.webp";
import playstoreqr from "../../../public/images/login-daskbox/playstoreqr.png";
import vectord from "../../../public/images/login-daskbox/supportLogo.png";

const ProfilePage = () => {
  const [userDetails, setUserDetails] = useState({});
  const [isLoading, setIsLoading] = useState(false);
  const [cancelStatus, setCancelStatus] = useState({ success: false, error: null });

  useEffect(() => {
    const storedUserDetails = JSON.parse(localStorage.getItem("UserDetails")) || {};
    setUserDetails(storedUserDetails);
  }, []);
  const { email, expiryDate, StripeId } = userDetails;
  const date = new Date(expiryDate);
  const router = useRouter();
  const t = useTranslations("Dashboard");
  const { logout, isSubscribed } = useAuth();

  const logoutHandler = () => {
    router.replace("/login");
    logout();
  };

  const handleCancelSubscription = async () => {
    if (!confirm("Are you sure you want to cancel your subscription?")) {
      return;
    }

    setIsLoading(true);
    setCancelStatus({ success: false, error: null });

    try {
      const response = await apiClient.get(
        `${process.env.NEXT_PUBLIC_SUBSSERVICE_BASE_URL}/subscription/cancel`
      );

      if (response.status === 200) {
        setCancelStatus({ success: true, error: null });

        // Update user details in localStorage
        const updatedUserDetails = { ...userDetails, isSubscribed: false };
        localStorage.setItem("UserDetails", JSON.stringify(updatedUserDetails));
        setUserDetails(updatedUserDetails);

        // Update auth context
        logout();
        setTimeout(() => {
          router.replace("/login");
        }, 2000);
      }
    } catch (error) {
      console.error("Error cancelling subscription:", error);
      setCancelStatus({
        success: false,
        error:
          error.response?.data?.message || "Failed to cancel subscription. Please try again later.",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-white mt-8">
      <div className="mx-auto flex flex-col">
        <div className="bg-white rounded-lg shadow-[0_1px_3px_0_rgba(0,0,0,0.1),0_1px_2px_0_rgba(0,0,0,0.06)] mb-6 p-8 w-[818px] bg-[radial-gradient(95.78%_61.92%_at_50%_35.25%,#cbafff_0%,#9258fe_100%)] text-white flex flex-col items-center justify-between max-w-[765px] self-center p-4 text-center overflow-hidden">
          <div className="flex flex-col items-center text-left gap-4 pl-4">
            {isSubscribed && (
              <p className="text-[1.5rem] text-[#e2e2e2] leading-8 font-bold font-nevermind-display break-words">
                {t("DashboardPlanLine1")} <br />
                {t("DashboardPlanLine2")}
                <br />
                {t("DashboardPlanLine3")}
              </p>
            )}
            {!isSubscribed && (
              <p className="text-[1.5rem] text-[#e2e2e2] leading-8 font-bold font-nevermind-display break-words">
                {t("DashboardPlanLine4")} <br />
                {t("DashboardPlanLine5")}
                <br />
                <button
                  className="bg-white text-[#8b5cf6] shadow-[0px_5.35px_0px_0px_#4a2d7f] w-auto h-auto py-[6.4px] px-[32.11px] font-poppins rounded-[10.7px] font-medium border-none cursor-pointer"
                  onClick={() => {
                    router.push("/acquisition");
                  }}
                >
                  {t("DashboardAnnual")}
                </button>
              </p>
            )}
          </div>
          <Image
            src={daskbox}
            alt="Subscription illustration"
            className="pr-14 w-[210px] h-[173px]"
            width={210}
            height={173}
          />
        </div>

        <div style={{ textAlign: "-webkit-center" }}>
          <div className={styles.headerDiv}>
            <div className={styles.header}>
              <h1 className={styles.title}> {t("DashboardInformation")}</h1>
              <div>
                <button
                  className={`${styles.button} ${styles.ghostButton}`}
                  onClick={() => (window.location.href = "https://support.skidos.com/")}
                >
                  {t("DashboardSupport")}{" "}
                  <Image
                    style={{ marginLeft: "0.325rem" }}
                    src={vectord}
                    alt="Support"
                    width={13}
                    height={13}
                  />
                </button>
                <button
                  className={`${styles.button} ${styles.ghostButton}`}
                  onClick={logoutHandler}
                >
                  {t("DashboardLogout")}{" "}
                  <Image
                    style={{ marginLeft: "0.325rem" }}
                    src={vector}
                    alt="Logout"
                    width={13}
                    height={13}
                  />
                </button>
              </div>
            </div>
          </div>

          <p className={styles.email}>{email}</p>
          <div className={styles.headerDiv}>
            <div className={styles.header}>
              <h1 className={styles.subtitle}> {t("DashboardMembership")}</h1>
            </div>
          </div>
          <div className={styles.membershipInfoContent}>
            <div className={styles.membershipInfo}>
              <span className={styles.label}> {t("DashboardSubscript")}</span>
              <span className={`${styles.active} ${!isSubscribed ? styles.inactive : ""}`}>
                {isSubscribed ? t("DashboardActive") : t("DashboardInactive")}
              </span>
            </div>
            <div style={{ borderTop: "1px solid #B9B9B9" }}></div>
            {isSubscribed && (
              <div className={styles.membershipInfo}>
                <span className={styles.label}>{t("DashboardExpire")}</span>
                <span style={{ paddingRight: "1rem" }}>
                  {date.toLocaleDateString("en-GB", {
                    day: "numeric",
                    month: "long",
                    year: "numeric",
                  })}
                </span>
              </div>
            )}

            {isSubscribed && StripeId && (
              <div style={{ textAlign: "left" }}>
                <button
                  className={`${styles.button} ${styles.ghostButton}`}
                  onClick={handleCancelSubscription}
                  disabled={isLoading}
                  style={{ paddingLeft: 0, paddingTop: 0 }}
                >
                  {isLoading ? "Processing..." : t("DashboardCancelSubscription")}
                  <FontAwesomeIcon style={{ marginLeft: "0.325rem" }} icon={faArrowRight} />
                </button>

                {cancelStatus.success && (
                  <p style={{ color: "green", marginTop: "0.5rem" }}>
                    Your subscription has been successfully cancelled. You will be redirected
                    shortly.
                  </p>
                )}

                {cancelStatus.error && (
                  <p style={{ color: "red", marginTop: "0.5rem" }}>{cancelStatus.error}</p>
                )}
              </div>
            )}
          </div>
        </div>

        <div style={{ marginBottom: "1.6rem", textAlign: "-webkit-center" }}>
          <div className={styles.headerDiv}>
            <div className={styles.header} style={{ marginBottom: "1.6rem" }}>
              <h1 className={styles.subtitle}>{t("DashboardDownload")}</h1>
            </div>
          </div>
          <div className={styles.qrCodes}>
            <div>
              <Image
                src={appstoreqr}
                alt="App Store QR Code"
                className={styles.qrCode}
                width={200}
                height={200}
              />
              <p style={{ textAlign: "center" }}>{t("DashboardApp")}</p>
            </div>
            <div>
              <Image
                src={playstoreqr}
                alt="Play Store QR Code"
                className={styles.qrCode}
                width={200}
                height={200}
              />
              <p style={{ textAlign: "center" }}>{t("DashboardPlay")}</p>
            </div>
          </div>
        </div>

        <button
          className={`${styles.button} ${styles.exploreButton}`}
          onClick={() => {
            router.push(isSubscribed ? "/user-home-screen" : "/products");
          }}
        >
          {t("DashboardExplore")}
        </button>
      </div>
    </div>
  );
};
export default ProfilePage;
