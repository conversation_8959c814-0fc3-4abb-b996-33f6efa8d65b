"use client";
import CreatePlayerSection from "@/components/CreatePlayerSection";
import EnterEmailSection from "@/components/EnterEmailSection";
import GetStarted from "@/components/GetStartedSection";
import PersonaliseChildSection from "@/components/PersonaliseChildSection";
import Stepper from "@/components/Stepper";
import { PlayerProvider } from "@/context/CreatePlayerContext";
import useTogglePinkFooter from "@/hooks/useTogglePinkFooter";
import { trackWebEngageEvent } from "@/utils/webengage";
import { useEffect, useState } from "react";

const STEP_EVENTS = {
  1: "WebGetStartedReached",
  2: "WebPlayerCreationReached",
  3: "WebPersnalizationScrReached",
  4: "WebEmailScrReached",
};

const BUTTON_EVENTS = {
  getStarted: "WebGetStartedBtnClk",
  createPlayer: "WebPlayerCreationScrNextBtnClk",
  personalise: "WebPersnalizationScrNextBtnClk",
};

const CreatePlayer = () => {
  const [currentStep, setCurrentStep] = useState(1);

  useEffect(() => {
    if (STEP_EVENTS[currentStep]) {
      trackWebEngageEvent(STEP_EVENTS[currentStep]);
    }
  }, [currentStep]);

  const handleGetStarted = () => {
    trackWebEngageEvent(BUTTON_EVENTS.getStarted);
    setCurrentStep(2);
  };

  const handleCreatePlayerSubmit = () => {
    trackWebEngageEvent(BUTTON_EVENTS.createPlayer);
    setCurrentStep(3);
  };

  const handlePersonaliseChildSubmit = () => {
    trackWebEngageEvent(BUTTON_EVENTS.personalise);
    setCurrentStep(4);
  };

  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep((prevStep) => prevStep - 1);
    }
  };

  useTogglePinkFooter(true);

  return (
    <PlayerProvider>
      <div className="bg-[#f9f9f9] w-full">
        <div className="get-started-container">
          <Stepper currentStep={currentStep} handleBack={handleBack} />
          {currentStep === 4 ? (
            <EnterEmailSection />
          ) : currentStep === 3 ? (
            <PersonaliseChildSection onSubmit={handlePersonaliseChildSubmit} />
          ) : currentStep === 2 ? (
            <CreatePlayerSection onSubmit={handleCreatePlayerSubmit} />
          ) : (
            <GetStarted onGetStarted={handleGetStarted} />
          )}
        </div>
      </div>
    </PlayerProvider>
  );
};

export default CreatePlayer;
