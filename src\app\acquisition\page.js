"use client";
import Loader from "@/components/Loader";
import { HomeStreet } from "@/constants";
import useIsMobile from "@/hooks/useIsMobile";
import useStripeCheckout from "@/hooks/useStripeCheckout";
import apiClient from "@/utils/axiosUtil";
import { getLocale, toSentenceCase, transformTextWithBreak } from "@/utils/helperFunctions";
import { useLocale, useTranslations } from "next-intl";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import ChildMascots from "../../../public/images/acquisition/kids&mascots.webp";
import ChildMascotsMb from "../../../public/images/acquisition/kids&mascotsMb.webp";

const Acquisition = () => {
  const [plans, setPlans] = useState([]);
  const [loading, setLoading] = useState(false);
  const locale = useLocale();
  const t = useTranslations("Acquisition");
  const lang = getLocale(locale);
  const router = useRouter();
  const authToken = localStorage.getItem("auth_token");
  const { handleCheckout } = useStripeCheckout();

  useEffect(() => {
    getPlans();
  }, [locale]);

  const getPlans = async () => {
    setLoading(true);
    const url = `${process.env.NEXT_PUBLIC_SUBSSERVICE_BASE_URL}/plan?version=5.0&l=${lang}&planType=acquisition`;
    const response = await apiClient.get(url);
    setPlans(response.data);
    setLoading(false);
  };

  const activatePlan = (planID) => {
    localStorage.setItem("selectedPlan", planID);
    if (authToken) {
      handleCheckout({ lang, planID, authToken });
    } else {
      router.push("/get-started");
    }
  };

  const isMobile = useIsMobile();
  if (isMobile === null) {
    return null;
  }

  return (
    <div className="max-w-[1728px] mx-auto">
      <header className="bg-[url('/images/acquisition/acquisitionPageHeader.webp')] md:bg-[url('/images/acquisition/acquisitionPageHeaderMb.webp')] flex-[0_0_100%] h-[600px] md:h-[400px] bg-cover bg-center flex justify-center items-center flex-col max-w-[1728px] mx-auto">
        <div className="w-full h-[30%] md:h-[40%] text-white text-center flex flex-col items-center justify-center px-12 md:px-4 md:mt-4 box-border">
          <h1 className="text-[2.5rem] md:text-[1.8rem] m-0">{t("Header")}</h1>
          <p className="font-poppins font-normal text-[1.3rem] md:text-[1.2rem] m-0 px-40 md:px-2 box-border">{t("Subheader")}</p>
        </div>
        <div className="w-full h-[70%] md:h-[60%] relative w-full">
          {isMobile !== null && (
            <Image
              src={isMobile ? ChildMascotsMb : ChildMascots}
              alt="Kids and Mascots"
              layout="fill"
              objectFit="contain"
              objectPosition="top"
            />
          )}
        </div>
      </header>
      <main className={styles.mainContentWrapper}>
        <section className={styles.plansCardWrapper}>
          {loading ? (
            <Loader />
          ) : (
            plans &&
            // Filtering out the Best Value card
            (() => {
              const bestValueCard = plans.find((plan) => plan.BestValue === 1);
              const otherPlans = plans.filter((plan) => plan.BestValue !== 1);

              return (
                <div className={styles.plansContainer}>
                  {/* Rendering the first non-BestValue card */}
                  {otherPlans[0] && (
                    <div className={styles.normalPlanCard}>
                      <p className={styles.normalPlanCardHeading}>
                        {t(toSentenceCase(otherPlans[0].PlanName))}
                      </p>
                      <p className={styles.discountWrapper}>
                        {otherPlans[0].NonDiscountPrice !== "---" ? (
                          <>
                            <span className={styles.originalPrice}>
                              {otherPlans[0].PlanTextTransformed.nonDiscountedPrice}
                            </span>
                            {otherPlans[0].OfferText &&
                              otherPlans[0].OfferText.trim().length > 0 && (
                                <span className={styles.discount}>{otherPlans[0].OfferText}</span>
                              )}
                          </>
                        ) : null}
                      </p>
                      <p className={styles.discountedPrice}>{otherPlans[0].Price}</p>
                      <p className={styles.billedYearly}>
                        {/* {`${otherPlans[0].PlanTextTransformed.annualText.replace("(price)", otherPlans[0].PlanTextTransformed.cpm)} `} */}
                        {transformTextWithBreak(
                          otherPlans[0].PlanTextTransformed.annualText,
                          otherPlans[0].PlanTextTransformed.price,
                          "free"
                        )}
                      </p>
                      <button
                        className={`${styles.activateBtn}`}
                        onClick={() => activatePlan(otherPlans[0].PlanID)}
                      >
                        {t("ActivateBtn")}
                      </button>
                    </div>
                  )}

                  {/* Rendering the Best Value card in the center */}
                  {bestValueCard && (
                    <div className={styles.heroPlanCardWrapper}>
                      <div className={styles.bestValueContainer}>{t("BestValue")}</div>
                      <div className={styles.heroPlanCardContentWrapper}>
                        <div className={styles.heroPlanCardContent}>
                          <p className={styles.heroHeading}>
                            {t(toSentenceCase(bestValueCard.PlanName))}
                          </p>
                          <p className={styles.discountWrapper}>
                            {bestValueCard.NonDiscountPrice !== "---" ? (
                              <>
                                <span className={styles.heroDiscountedPrice}>
                                  {bestValueCard.NonDiscountPrice}
                                </span>
                                {bestValueCard.OfferText &&
                                  bestValueCard.OfferText.trim().length > 0 && (
                                    <span className={styles.heroBilledYearly}>
                                      {bestValueCard.OfferText}
                                    </span>
                                  )}
                              </>
                            ) : null}
                          </p>
                          <p className={styles.heroPlanPrice}>{bestValueCard.Price}</p>
                          <p className={styles.heroBilled}>
                            {transformTextWithBreak(
                              bestValueCard.PlanTextTransformed.annualText,
                              bestValueCard.PlanTextTransformed.price,
                              "after"
                            )}
                          </p>
                          <button
                            className={`${styles.heroPlanBtn}`}
                            onClick={() => activatePlan(bestValueCard.PlanID)}
                          >
                            {t("ActivateBtn")}
                          </button>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Rendering the second non-BestValue card */}
                  {otherPlans[1] && (
                    <div className={styles.normalPlanCard}>
                      <p className={styles.normalPlanCardHeading}>
                        {t(toSentenceCase(otherPlans[1].PlanName))}
                      </p>
                      <p className={styles.discountWrapper}>
                        {otherPlans[1].NonDiscountPrice !== "---" ? (
                          <>
                            <span className={styles.originalPrice}>
                              {otherPlans[1].PlanTextTransformed.nonDiscountedPrice}
                            </span>
                            {otherPlans[1].OfferText &&
                              otherPlans[1].OfferText.trim().length > 0 && (
                                <span className={styles.discount}>{otherPlans[1].OfferText}</span>
                              )}
                          </>
                        ) : null}
                      </p>
                      <p className={styles.discountedPrice}>{otherPlans[1].Price}</p>
                      <p className={styles.billedYearly}>
                        {transformTextWithBreak(
                          otherPlans[1].PlanTextTransformed.annualText,
                          otherPlans[1].PlanTextTransformed.price,
                          "after"
                        )}
                        {/* {otherPlans[1].Price} 
                   <br /> free trial ends  */}
                      </p>
                      <button
                        className={`${styles.activateBtn} noLinkStyle`}
                        onClick={() => activatePlan(otherPlans[1].PlanID)}
                      >
                        {t("ActivateBtn")}
                      </button>
                    </div>
                  )}
                </div>
              );
            })()
          )}
        </section>
        <h2 className={styles.acquisitionHeading}>{t("LearningSectionHeading")}</h2>
        <section className={styles.allAboutContainer}>
          <div className={styles.aboutCard}>
            <Image
              src="/images/acquisition/About1.webp"
              alt="Kids and Mascots"
              width={330}
              height={230}
              className={styles.aboutContImg}
            />
            <p>{t("LearningSection1")}</p>
          </div>
          <div className={styles.aboutCard}>
            <Image
              src="/images/acquisition/About2.webp"
              alt="Kids and Mascots"
              width={330}
              height={230}
              className={styles.aboutContImg}
            />
            <p>{t("LearningSection2")}</p>
          </div>
          <div className={styles.aboutCard}>
            <Image
              src="/images/acquisition/About3.webp"
              alt="Kids and Mascots"
              width={330}
              height={230}
              className={styles.aboutContImg}
            />
            <p> {t("LearningSection3")}</p>
          </div>
          <div className={styles.aboutCard}>
            <Image
              src="/images/acquisition/About4.webp"
              alt="Kids and Mascots"
              width={330}
              height={230}
              className={styles.aboutContImg}
            />
            <p>{t("LearningSection4")}</p>
          </div>
          <div className={styles.aboutCard}>
            <Image
              src="/images/acquisition/About5.webp"
              alt="Kids and Mascots"
              width={330}
              height={230}
              className={styles.aboutContImg}
            />
            <p>{t("LearningSection5")}</p>
          </div>
          <div className={styles.aboutCard}>
            <Image
              src="/images/acquisition/About6.webp"
              alt="Kids and Mascots"
              width={330}
              height={230}
              className={styles.aboutContImg}
            />
            <p>{t("LearningSection6")}</p>
          </div>
          <div className={styles.aboutCard}>
            <Image
              src="/images/acquisition/About7.webp"
              alt="Kids and Mascots"
              width={330}
              height={230}
              className={styles.aboutContImg}
            />
            <p>{t("LearningSection7")}</p>
          </div>
          <div className={styles.aboutCard}>
            <Image
              src="/images/acquisition/About8.webp"
              alt="Kids and Mascots"
              width={330}
              height={230}
              className={styles.aboutContImg}
            />
            <p>{t("LearningSection8")}</p>
          </div>
        </section>
        <h2 className={styles.acquisitionHeading}>{t("AwardHeader")}</h2>
        <div className={styles.awardsWrapper}>
          <div className="awardImgWrapper">
            <Image
              src="/images/Award1.webp"
              height={150}
              width={150}
              alt="Award"
              className={styles.awardImg}
            />
            <Image
              src="/images/Award2.webp"
              height={150}
              width={150}
              alt="Award"
              className={styles.awardImg}
            />
            <Image
              src="/images/Award3.webp"
              height={150}
              width={150}
              alt="Award"
              className={styles.awardImg}
            />
            <Image
              src="/images/Award4.webp"
              height={150}
              width={150}
              alt="Award"
              className={styles.awardImg}
            />
            <Image
              src="/images/Award5.webp"
              height={150}
              width={150}
              alt="Award"
              className={styles.awardImg}
            />
            <Image
              src="/images/Award6.webp"
              height={150}
              width={150}
              alt="Award"
              className={styles.awardImg}
            />
            <Image
              src="/images/Award7.png"
              height={150}
              width={150}
              alt="Award"
              className={styles.awardImg}
            />
          </div>
        </div>
        <h2 className={styles.acquisitionHeading}>{t("TestimonialHeader")}</h2>
        <div className={styles.reviewWrapper}>
          {HomeStreet.map((item, index) => (
            <div key={index} className={styles.reviewCard}>
              <div>
                <Image src={item.starImg} width={152} height={24} alt="Rating Stars" />
                <p>{t(item.content)}</p>
              </div>
              <div className={styles.reviewCardFooter}>
                <p>{t(item.description)}</p>
              </div>
            </div>
          ))}
        </div>
      </main>
    </div>
  );
};

export default Acquisition;
