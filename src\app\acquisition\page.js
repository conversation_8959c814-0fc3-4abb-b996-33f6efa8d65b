"use client";
import Loader from "@/components/Loader";
import { HomeStreet } from "@/constants";
import useIsMobile from "@/hooks/useIsMobile";
import useStripeCheckout from "@/hooks/useStripeCheckout";
import apiClient from "@/utils/axiosUtil";
import { getLocale, toSentenceCase, transformTextWithBreak } from "@/utils/helperFunctions";
import { useLocale, useTranslations } from "next-intl";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import ChildMascots from "../../../public/images/acquisition/kids&mascots.webp";
import ChildMascotsMb from "../../../public/images/acquisition/kids&mascotsMb.webp";

const Acquisition = () => {
  const [plans, setPlans] = useState([]);
  const [loading, setLoading] = useState(false);
  const locale = useLocale();
  const t = useTranslations("Acquisition");
  const lang = getLocale(locale);
  const router = useRouter();
  const authToken = localStorage.getItem("auth_token");
  const { handleCheckout } = useStripeCheckout();

  useEffect(() => {
    getPlans();
  }, [locale]);

  const getPlans = async () => {
    setLoading(true);
    const url = `${process.env.NEXT_PUBLIC_SUBSSERVICE_BASE_URL}/plan?version=5.0&l=${lang}&planType=acquisition`;
    const response = await apiClient.get(url);
    setPlans(response.data);
    setLoading(false);
  };

  const activatePlan = (planID) => {
    localStorage.setItem("selectedPlan", planID);
    if (authToken) {
      handleCheckout({ lang, planID, authToken });
    } else {
      router.push("/get-started");
    }
  };

  const isMobile = useIsMobile();
  if (isMobile === null) {
    return null;
  }

  return (
    <div className="max-w-[1728px] mx-auto">
      <header className="bg-[url('/images/acquisition/acquisitionPageHeader.webp')] md:bg-[url('/images/acquisition/acquisitionPageHeaderMb.webp')] flex-[0_0_100%] h-[600px] md:h-[400px] bg-cover bg-center flex justify-center items-center flex-col max-w-[1728px] mx-auto">
        <div className="w-full h-[30%] md:h-[40%] text-white text-center flex flex-col items-center justify-center px-12 md:px-4 md:mt-4 box-border">
          <h1 className="text-[2.5rem] md:text-[1.8rem] m-0">{t("Header")}</h1>
          <p className="font-poppins font-normal text-[1.3rem] md:text-[1.2rem] m-0 px-40 md:px-2 box-border">
            {t("Subheader")}
          </p>
        </div>
        <div className="w-full h-[70%] md:h-[60%] relative w-full">
          {isMobile !== null && (
            <Image
              src={isMobile ? ChildMascotsMb : ChildMascots}
              alt="Kids and Mascots"
              layout="fill"
              objectFit="contain"
              objectPosition="top"
            />
          )}
        </div>
      </header>
      <main className="relative -top-40 text-center lg:-top-44 md:-top-32 max-[600px]:-top-20">
        <section className="flex justify-center gap-8 lg:gap-6 md:gap-3 max-[600px]:flex-col">
          {loading ? (
            <Loader />
          ) : (
            plans &&
            // Filtering out the Best Value card
            (() => {
              const bestValueCard = plans.find((plan) => plan.BestValue === 1);
              const otherPlans = plans.filter((plan) => plan.BestValue !== 1);

              return (
                <div className="flex justify-center gap-5 max-[600px]:flex-col">
                  {/* Rendering the first non-BestValue card */}
                  {otherPlans[0] && (
                    <div className="bg-white border border-[#d4d4d4] rounded-[2rem] flex flex-col text-center items-center py-[1.4rem] px-[1.4rem] self-end transition-transform duration-300 ease min-w-[180px] box-border w-[340px] h-[325px] justify-between hover:cursor-pointer hover:scale-[1.02] lg:rounded-[1.8rem] lg:py-[1.2rem] lg:px-[1.2rem] lg:w-[260px] lg:h-[285px] md:rounded-[1.3rem] md:py-4 md:px-4 md:w-[200px] md:h-[205px] max-[600px]:self-center max-[600px]:order-2 max-[600px]:min-w-[250px] max-[600px]:w-[270px] max-[600px]:h-[265px]">
                      <p className="text-[1.9rem] m-0 lg:text-[1.5rem] md:text-base">
                        {t(toSentenceCase(otherPlans[0].PlanName))}
                      </p>
                      <p className="flex items-center gap-[10px] lg:gap-[5px] md:gap-[5px]">
                        {otherPlans[0].NonDiscountPrice !== "---" ? (
                          <>
                            <span className="text-black/70 text-[1.4rem] line-through lg:text-base md:text-[0.7rem]">
                              {otherPlans[0].PlanTextTransformed.nonDiscountedPrice}
                            </span>
                            {otherPlans[0].OfferText &&
                              otherPlans[0].OfferText.trim().length > 0 && (
                                <span className="bg-[#1dc368] py-[0.3rem] px-[0.5rem] text-base rounded-[0.7rem] lg:py-[0.3rem] lg:px-[0.5rem] lg:text-[0.8rem] lg:rounded-[0.7rem] md:py-[0.2rem] md:px-[0.3rem] md:text-[0.6rem] md:rounded-[0.5rem]">
                                  {otherPlans[0].OfferText}
                                </span>
                              )}
                          </>
                        ) : null}
                      </p>
                      <p className="text-[1.9rem] m-0 lg:text-[1.5rem] md:text-base">
                        {otherPlans[0].Price}
                      </p>
                      <p className="text-base font-poppins font-medium lg:text-base md:text-[0.7rem]">
                        {/* {`${otherPlans[0].PlanTextTransformed.annualText.replace("(price)", otherPlans[0].PlanTextTransformed.cpm)} `} */}
                        {transformTextWithBreak(
                          otherPlans[0].PlanTextTransformed.annualText,
                          otherPlans[0].PlanTextTransformed.price,
                          "free"
                        )}
                      </p>
                      <button
                        className="w-full bg-[#eee5ff] rounded-[0.8rem] py-[0.8rem] px-[0.5rem] text-[1.5rem] text-[#4a2d80] font-nevermind-bold cursor-pointer border-none lg:rounded-[0.6rem] lg:py-[0.5rem] lg:px-[0.3rem] lg:text-[1.2rem] md:rounded-[0.5rem] md:py-[0.4rem] md:px-[0.2rem] md:text-[0.8rem]"
                        onClick={() => activatePlan(otherPlans[0].PlanID)}
                      >
                        {t("ActivateBtn")}
                      </button>
                    </div>
                  )}

                  {/* Rendering the Best Value card in the center */}
                  {bestValueCard && (
                    <div className="relative max-[600px]:flex max-[600px]:justify-center max-[600px]:order-1">
                      <div className="bg-white absolute left-1/2 transform -translate-x-1/2 w-[190px] py-[0.7rem] px-[0.8rem] -top-[15px] rounded-[20px] font-poppins font-medium text-[1.2rem] transition-all duration-300 ease-out hover:-top-[50px] hover:w-[150px] hover:rounded-[40px] lg:w-[150px] lg:py-[0.6rem] lg:px-[0.8rem] lg:-top-[15px] lg:rounded-[20px] lg:text-base lg:hover:w-[110px] md:w-[120px] md:py-[0.6rem] md:px-[0.8rem] md:-top-[15px] md:text-[0.8rem] md:hover:-top-[35px] md:hover:w-[90px] max-[600px]:w-[150px] max-[600px]:text-center max-[600px]:-top-[10px] max-[600px]:py-[0.5rem] max-[600px]:px-[0.7rem] max-[600px]:pb-4 max-[600px]:rounded-[20px] max-[600px]:text-[1.2rem] max-[600px]:hover:w-[120px] max-[600px]:hover:left-1/2 max-[600px]:hover:-top-[35px] max-[600px]:hover:rounded-[40px] max-[600px]:hover:py-[0.5rem] max-[600px]:hover:px-[0.6rem]">
                        {t("BestValue")}
                      </div>
                      <div className="w-[380px] h-[380px] bg-[url('/images/acquisition/heroCardBg.svg')] bg-cover bg-center relative z-10 transition-transform duration-300 ease hover:scale-[1.02] hover:cursor-pointer lg:w-[320px] lg:h-[320px] md:w-[256px] md:h-[256px] max-[600px]:w-[300px] max-[600px]:h-[300px]">
                        <div className="flex flex-col items-center justify-center h-full my-0 mx-8 md:my-0 md:mx-6">
                          <p className="text-[2.4rem] text-white m-0 mt-4 lg:text-[2rem] lg:text-white lg:m-0 lg:mt-4 md:text-[1.5rem] md:text-white md:m-0 md:mt-4 max-[600px]:text-[2rem]">
                            {t(toSentenceCase(bestValueCard.PlanName))}
                          </p>
                          <p className="flex items-center gap-[10px] lg:gap-[5px] md:gap-[5px]">
                            {bestValueCard.NonDiscountPrice !== "---" ? (
                              <>
                                <span className="text-white/70 text-[1.4rem] line-through lg:text-[1.2rem] md:text-base">
                                  {bestValueCard.NonDiscountPrice}
                                </span>
                                {bestValueCard.OfferText &&
                                  bestValueCard.OfferText.trim().length > 0 && (
                                    <span className="text-[1.1rem] bg-[#1dc368] py-[0.25rem] px-[0.5rem] text-white rounded-[10px] lg:text-base lg:py-[0.3rem] lg:px-[0.5rem] lg:rounded-[10px] md:text-[0.8rem] md:py-[0.2rem] md:px-[0.4rem] md:rounded-[8px] max-[600px]:text-[1.1rem] max-[600px]:py-[0.3rem] max-[600px]:px-[0.5rem]">
                                      {bestValueCard.OfferText}
                                    </span>
                                  )}
                              </>
                            ) : null}
                          </p>
                          <p className="text-white text-[2.4rem] m-0 lg:text-white lg:text-[2rem] lg:m-0 md:text-[1.5rem] max-[600px]:text-[2rem]">
                            {bestValueCard.Price}
                          </p>
                          <p className="text-center text-[1.1rem] font-poppins text-white p-0 lg:text-center lg:text-[0.8rem] lg:font-poppins lg:text-white lg:p-0 md:text-[0.8rem] max-[600px]:text-[1.2rem]">
                            {transformTextWithBreak(
                              bestValueCard.PlanTextTransformed.annualText,
                              bestValueCard.PlanTextTransformed.price,
                              "after"
                            )}
                          </p>
                          <button
                            className="w-full rounded-4 bg-[#9258fe] text-white py-[0.8rem] px-[0.5rem] text-[2rem] border-none cursor-pointer font-nevermind-bold lg:py-[0.5rem] lg:px-[0.5rem] lg:text-[1.5rem] lg:rounded-[0.8rem] md:py-[0.5rem] md:px-[0.3rem] md:text-[1.2rem] md:rounded-[0.5rem] max-[600px]:rounded-[0.8rem] max-[600px]:py-[0.9rem] max-[600px]:px-[0.5rem] max-[600px]:text-[1.5rem]"
                            onClick={() => activatePlan(bestValueCard.PlanID)}
                          >
                            {t("ActivateBtn")}
                          </button>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Rendering the second non-BestValue card */}
                  {otherPlans[1] && (
                    <div className="bg-white border border-[#d4d4d4] rounded-[2rem] flex flex-col text-center items-center py-[1.4rem] px-[1.4rem] self-end transition-transform duration-300 ease min-w-[180px] box-border w-[340px] h-[325px] justify-between hover:cursor-pointer hover:scale-[1.02] lg:rounded-[1.8rem] lg:py-[1.2rem] lg:px-[1.2rem] lg:w-[260px] lg:h-[285px] md:rounded-[1.3rem] md:py-4 md:px-4 md:w-[200px] md:h-[205px] max-[600px]:self-center max-[600px]:order-2 max-[600px]:min-w-[250px] max-[600px]:w-[270px] max-[600px]:h-[265px]">
                      <p className="text-[1.9rem] m-0 lg:text-[1.5rem] md:text-base">
                        {t(toSentenceCase(otherPlans[1].PlanName))}
                      </p>
                      <p className="flex items-center gap-[10px] lg:gap-[5px] md:gap-[5px]">
                        {otherPlans[1].NonDiscountPrice !== "---" ? (
                          <>
                            <span className="text-black/70 text-[1.4rem] line-through lg:text-base md:text-[0.7rem]">
                              {otherPlans[1].PlanTextTransformed.nonDiscountedPrice}
                            </span>
                            {otherPlans[1].OfferText &&
                              otherPlans[1].OfferText.trim().length > 0 && (
                                <span className="bg-[#1dc368] py-[0.3rem] px-[0.5rem] text-base rounded-[0.7rem] lg:py-[0.3rem] lg:px-[0.5rem] lg:text-[0.8rem] lg:rounded-[0.7rem] md:py-[0.2rem] md:px-[0.3rem] md:text-[0.6rem] md:rounded-[0.5rem]">
                                  {otherPlans[1].OfferText}
                                </span>
                              )}
                          </>
                        ) : null}
                      </p>
                      <p className="text-[1.9rem] m-0 lg:text-[1.5rem] md:text-base">
                        {otherPlans[1].Price}
                      </p>
                      <p className="text-base font-poppins font-medium lg:text-base md:text-[0.7rem]">
                        {transformTextWithBreak(
                          otherPlans[1].PlanTextTransformed.annualText,
                          otherPlans[1].PlanTextTransformed.price,
                          "after"
                        )}
                        {/* {otherPlans[1].Price}
                   <br /> free trial ends  */}
                      </p>
                      <button
                        className="w-full bg-[#eee5ff] rounded-[0.8rem] py-[0.8rem] px-[0.5rem] text-[1.5rem] text-[#4a2d80] font-nevermind-bold cursor-pointer border-none lg:rounded-[0.6rem] lg:py-[0.5rem] lg:px-[0.3rem] lg:text-[1.2rem] md:rounded-[0.5rem] md:py-[0.4rem] md:px-[0.2rem] md:text-[0.8rem] no-underline"
                        onClick={() => activatePlan(otherPlans[1].PlanID)}
                      >
                        {t("ActivateBtn")}
                      </button>
                    </div>
                  )}
                </div>
              );
            })()
          )}
        </section>
        <h2 className="my-16 mx-0 mb-8 text-[2rem]">{t("LearningSectionHeading")}</h2>
        <section className="flex justify-center gap-10 flex-wrap max-w-[90%] mx-auto max-[600px]:gap-7">
          <div className="w-[330px] text-center font-poppins font-medium text-[#023066] md:w-[280px]">
            <Image
              src="/images/acquisition/About1.webp"
              alt="Kids and Mascots"
              width={330}
              height={230}
              className="md:w-[280px] md:h-[200px]"
            />
            <p className="text-[1.5rem] font-poppins font-medium">{t("LearningSection1")}</p>
          </div>
          <div className="w-[330px] text-center font-poppins font-medium text-[#023066] md:w-[280px]">
            <Image
              src="/images/acquisition/About2.webp"
              alt="Kids and Mascots"
              width={330}
              height={230}
              className="md:w-[280px] md:h-[200px]"
            />
            <p className="text-[1.5rem] font-poppins font-medium">{t("LearningSection2")}</p>
          </div>
          <div className="w-[330px] text-center font-poppins font-medium text-[#023066] md:w-[280px]">
            <Image
              src="/images/acquisition/About3.webp"
              alt="Kids and Mascots"
              width={330}
              height={230}
              className="md:w-[280px] md:h-[200px]"
            />
            <p> {t("LearningSection3")}</p>
          </div>
          <div className="w-[330px] text-center font-poppins font-medium text-[#023066] md:w-[280px]">
            <Image
              src="/images/acquisition/About4.webp"
              alt="Kids and Mascots"
              width={330}
              height={230}
              className="md:w-[280px] md:h-[200px]"
            />
            <p className="text-[1.5rem] font-poppins font-medium">{t("LearningSection4")}</p>
          </div>
          <div className="w-[330px] text-center font-poppins font-medium text-[#023066] md:w-[280px]">
            <Image
              src="/images/acquisition/About5.webp"
              alt="Kids and Mascots"
              width={330}
              height={230}
              className="md:w-[280px] md:h-[200px]"
            />
            <p className="text-[1.5rem] font-poppins font-medium">{t("LearningSection5")}</p>
          </div>
          <div className="w-[330px] text-center font-poppins font-medium text-[#023066] md:w-[280px]">
            <Image
              src="/images/acquisition/About6.webp"
              alt="Kids and Mascots"
              width={330}
              height={230}
              className="md:w-[280px] md:h-[200px]"
            />
            <p className="text-[1.5rem] font-poppins font-medium">{t("LearningSection6")}</p>
          </div>
          <div className="w-[330px] text-center font-poppins font-medium text-[#023066] md:w-[280px]">
            <Image
              src="/images/acquisition/About7.webp"
              alt="Kids and Mascots"
              width={330}
              height={230}
              className="md:w-[280px] md:h-[200px]"
            />
            <p className="text-[1.5rem] font-poppins font-medium">{t("LearningSection7")}</p>
          </div>
          <div className="w-[330px] text-center font-poppins font-medium text-[#023066] md:w-[280px]">
            <Image
              src="/images/acquisition/About8.webp"
              alt="Kids and Mascots"
              width={330}
              height={230}
              className="md:w-[280px] md:h-[200px]"
            />
            <p className="text-[1.5rem] font-poppins font-medium">{t("LearningSection8")}</p>
          </div>
        </section>
        <h2 className="my-16 mx-0 mb-8 text-[2rem]">{t("AwardHeader")}</h2>
        <div className="flex justify-center items-center gap-8 flex-wrap max-w-[90%] mx-auto">
          <div className="awardImgWrapper">
            <Image
              src="/images/Award1.webp"
              height={150}
              width={150}
              alt="Award"
              className="w-[150px] h-[150px] object-contain"
            />
            <Image
              src="/images/Award2.webp"
              height={150}
              width={150}
              alt="Award"
              className="w-[150px] h-[150px] object-contain"
            />
            <Image
              src="/images/Award3.webp"
              height={150}
              width={150}
              alt="Award"
              className="w-[150px] h-[150px] object-contain"
            />
            <Image
              src="/images/Award4.webp"
              height={150}
              width={150}
              alt="Award"
              className="w-[150px] h-[150px] object-contain"
            />
            <Image
              src="/images/Award5.webp"
              height={150}
              width={150}
              alt="Award"
              className="w-[150px] h-[150px] object-contain"
            />
            <Image
              src="/images/Award6.webp"
              height={150}
              width={150}
              alt="Award"
              className="w-[150px] h-[150px] object-contain"
            />
            <Image
              src="/images/Award7.png"
              height={150}
              width={150}
              alt="Award"
              className="w-[150px] h-[150px] object-contain"
            />
          </div>
        </div>
        <h2 className="my-16 mx-0 mb-8 text-[2rem]">{t("TestimonialHeader")}</h2>
        <div className="flex justify-center gap-8 flex-wrap max-w-[90%] mx-auto">
          {HomeStreet.map((item, index) => (
            <div
              key={index}
              className="bg-white p-6 rounded-lg shadow-md border border-[#e0e0e0] max-w-[300px] text-center"
            >
              <div>
                <Image src={item.starImg} width={152} height={24} alt="Rating Stars" />
                <p className="text-[1rem] font-poppins my-4">{t(item.content)}</p>
              </div>
              <div className="border-t border-[#e0e0e0] pt-4 mt-4">
                <p className="text-[0.9rem] font-poppins text-[#666] m-0">{t(item.description)}</p>
              </div>
            </div>
          ))}
        </div>
      </main>
    </div>
  );
};

export default Acquisition;
