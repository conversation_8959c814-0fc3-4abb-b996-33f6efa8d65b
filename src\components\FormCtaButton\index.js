// import styles from "./styles.module.css";

const FormCtaButton = ({ disabled = false, text, onClick, loading, customStyles = "" }) => {
  return (
    <button
      disabled={disabled}
      className={`btn-primary btn-large justify-items-center mt-4 px-26 w-auto ${customStyles}`}
      onClick={onClick}
    >
      {loading ? (
        <div className="flex justify-center items-center w-full">
          <div className="border-[6px] border-gray-200 border-t-primary-400 rounded-full w-[30px] h-[30px] animate-spin"></div>
        </div>
      ) : (
        text
      )}
    </button>
  );
};

export default FormCtaButton;
