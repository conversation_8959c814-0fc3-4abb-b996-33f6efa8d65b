// import styles from "./styles.module.css";

const FormCtaButton = ({ disabled = false, text, onClick, loading, customStyles = "" }) => {
  return (
    <button
      disabled={disabled}
      className={`justify-items-center mt-4 bg-primary-400 text-3xl text-white rounded-2xl py-2 px-26 no-underline border-none shadow-button cursor-pointer w-auto font-nevermind-medium max-[480px]:text-2xl ${customStyles}`}
      onClick={onClick}
    >
      {loading ? (
        <div className="flex justify-center items-center self-center content-center w-full">
          <div className="flex justify-center items-center justify-self-center place-self-center self-center content-center border-[6px] border-gray-200 border-t-primary-400 rounded-full w-[30px] h-[30px] animate-spin"></div>
        </div>
      ) : (
        text
      )}
    </button>
  );
};

export default FormCtaButton;
