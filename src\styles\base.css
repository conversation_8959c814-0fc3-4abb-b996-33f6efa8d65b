/* Base Styles - Core typography, fonts, and global settings */

:root {
  --font-nevermind-bold: "NeverMindBold", sans-serif;
  --font-nevermind-light: "NeverMindLight", sans-serif;
  --font-poppins: "PoppinsFont", sans-serif;
  --font-nevermind-medium: "NeverMindMedium", sans-serif;
}

html,
body {
  margin: 0;
  padding: 0;
  font-family: var(--font-nevermind-bold);
}

/* Base font size for rem calculations */
:root {
  font-size: 16px;
}

/* Responsive adjustments for smaller screens */
@media screen and (max-width: 768px) {
  :root {
    font-size: 14px;
  }
}

@media screen and (max-width: 480px) {
  :root {
    font-size: 12px;
  }
}

/* Font Family Base Classes */
.font-nevermind-bold {
  font-family: var(--font-nevermind-bold);
}

.font-nevermind-medium {
  font-family: var(--font-nevermind-medium);
}

.font-nevermind-light {
  font-family: var(--font-nevermind-light);
}

.font-poppins {
  font-family: var(--font-poppins);
}

/* Font Weight Classes */
.font-weight-light {
  font-weight: 300;
}

.font-weight-normal {
  font-weight: 400;
}

.font-weight-medium {
  font-weight: 500;
}

.font-weight-semibold {
  font-weight: 600;
}

.font-weight-bold {
  font-weight: 700;
}
