"use client";
import FormCtaButton from "@/components/FormCtaButton";
import { inforPopupData } from "@/constants";
import { usePlayerContext } from "@/context/CreatePlayerContext";
import { useTranslations } from "next-intl";
import Image from "next/image";
import { useState } from "react";
import InfoPopup from "../WebGlInfoPopup";
// import styles from "./styles.module.css";

const avatarsInfo = [
  {
    name: "<PERSON>",
    imagePath: "/images/webGl/avatar/sara_f_17.webp",
    index: 17,
    gender: "female",
  },
  {
    name: "<PERSON>",
    imagePath: "/images/webGl/avatar/alex_m_12.webp",
    index: 12,
    gender: "male",
  },
  {
    name: "<PERSON>",
    imagePath: "/images/webGl/avatar/alex_m_18.webp",
    index: 18,
    gender: "male",
  },
  {
    name: "<PERSON>",
    imagePath: "/images/webGl/avatar/alex_m_6.webp",
    index: 6,
    gender: "male",
  },
  {
    name: "<PERSON>",
    imagePath: "/images/webGl/avatar/christina_f_4.webp",
    index: 4,
    gender: "female",
  },
  {
    name: "Deepti",
    imagePath: "/images/webGl/avatar/deepti_f_7.webp",
    index: 7,
    gender: "female",
  },
  {
    name: "Faizan",
    imagePath: "/images/webGl/avatar/faizan_m_11.webp",
    index: 11,
    gender: "male",
  },
  {
    name: "Gary",
    imagePath: "/images/webGl/avatar/gary_m_2.webp",
    index: 2,
    gender: "male",
  },
  {
    name: "Hanna",
    imagePath: "/images/webGl/avatar/hanna_f_16.webp",
    index: 16,
    gender: "female",
  },
  {
    name: "Hanna",
    imagePath: "/images/webGl/avatar/hanna_f_19.webp",
    index: 19,
    gender: "female",
  },
  {
    name: "Hanna",
    imagePath: "/images/webGl/avatar/hanna_f_9.webp",
    index: 9,
    gender: "female",
  },
  {
    name: "Jim",
    imagePath: "/images/webGl/avatar/jim_m_10.webp",
    index: 10,
    gender: "male",
  },
  {
    name: "Lee",
    imagePath: "/images/webGl/avatar/lee_m_15.webp",
    index: 15,
    gender: "male",
  },
  {
    name: "Lee",
    imagePath: "/images/webGl/avatar/lee_m_8.webp",
    index: 8,
    gender: "male",
  },
  {
    name: "Mary",
    imagePath: "/images/webGl/avatar/mary_f_3.webp",
    index: 3,
    gender: "female",
  },
  {
    name: "Niki",
    imagePath: "/images/webGl/avatar/niki_f_13.webp",
    index: 13,
    gender: "female",
  },
  {
    name: "Pankaj",
    imagePath: "/images/webGl/avatar/pankaj_m_5.webp",
    index: 5,
    gender: "male",
  },
  {
    name: "Sakura",
    imagePath: "/images/webGl/avatar/sakura_f_0.webp",
    index: 0,
    gender: "female",
  },
  {
    name: "Salma",
    imagePath: "/images/webGl/avatar/salma_f_14.webp",
    index: 14,
    gender: "female",
  },
  {
    name: "Sam",
    imagePath: "/images/webGl/avatar/sam_m_1.webp",
    index: 1,
    gender: "male",
  },
];

const CreatePlayerSection = ({ onSubmit }) => {
  const { playerData, updatePlayerData } = usePlayerContext();
  const isReturningUser = playerData.name && playerData.selectedAge && playerData.selectedAvatar;
  const [name, setName] = useState(playerData.name || "");
  const [isNameSubmitted, setIsNameSubmitted] = useState(false);
  const [isNameFocused, setIsNameFocused] = useState(false);
  const [isAvatarSubmitted, setAvatarSubmitted] = useState(false);
  const [selectedAge, setSelectedAge] = useState(playerData.selectedAge || null);
  const [selectedAvatar, setSelectedAvatar] = useState(playerData.selectedAvatar || "");
  const [errors, setErrors] = useState({
    nameError: "",
    ageError: "",
    avatarSelectedError: "",
  });
  const [showPopup, setShowPopup] = useState(false);
  const t = useTranslations("CreatePlayer");

  const [currentSection, setCurrentSection] = useState("name");

  const handleNameInput = (e) => {
    const value = e.target.value;
    const validatedValue = value.replace(/[^a-zA-Z\s]/g, "").slice(0, 10);
    setName(validatedValue);

    if (validatedValue.trim().length >= 1) {
      setErrors((prevErrors) => ({ ...prevErrors, nameError: "" }));
      setIsNameSubmitted(true);
      setCurrentSection("avatar");
    } else {
      setErrors((prevErrors) => ({
        ...prevErrors,
        nameError: t("ErrorMsgAll"),
      }));
      setIsNameSubmitted(false);
      setCurrentSection("name"); // Keep in name section if invalid
    }
  };

  const handleNameFocus = () => {
    setIsNameFocused(true);
    setCurrentSection("name");
  };

  const handleNameBlur = (e) => {
    setIsNameFocused(false);
    handleNameInput(e);
  };

  const handleAvatarSelect = (avatar) => {
    setSelectedAvatar(avatar);
    setErrors((prevErrors) => ({ ...prevErrors, avatarSelectedError: "" }));
    setAvatarSubmitted(true);
    setCurrentSection("age"); // Move to age section after avatar selection
  };

  const handleAgeClick = (age) => {
    setSelectedAge(age);
    setErrors((prevErrors) => ({ ...prevErrors, ageError: "" }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    let hasErrors = false;
    const newErrors = {
      nameError: "",
      avatarSelectedError: "",
      ageError: "",
    };

    if (name.trim().length < 1) {
      newErrors.nameError = t("ErrorMsgAll");
      hasErrors = true;
    }
    if (!selectedAvatar) {
      newErrors.avatarSelectedError = t("ErrorMsgAll");
      hasErrors = true;
    }
    if (!selectedAge) {
      newErrors.ageError = t("ErrorMsgAll");
      hasErrors = true;
    }

    setErrors(newErrors);

    if (!hasErrors) {
      updatePlayerData({ name, selectedAge, selectedAvatar });
      onSubmit();
    }
  };

  const borderClass =
    errors.nameError && currentSection === "name"
      ? "border-[1.5px] border-red-500"
      : isNameFocused && name.trim().length >= 1
        ? "border-[1.5px] border-[#32c825]"
        : "border-[1.5px] border-black/25";

  const avatarWrapperClass = `flex flex-col py-1 px-4 rounded-[10px] transition-all duration-1000 ease-in-out
  ${isNameSubmitted && !isReturningUser ? "opacity-100 pointer-events-auto border border-black/25 shadow-[0_4px_10px_rgba(0,0,0,0.1)] pb-4" : ""}
  ${isAvatarSubmitted && !isReturningUser ? "!border-none !shadow-none" : ""}
  ${!isReturningUser ? "opacity-20 pointer-events-none" : ""}
  ${errors.avatarSelectedError && currentSection === "avatar" ? "border-[1.5px] border-red-500" : ""}`;

  const ageWrapperClass = `flex flex-col py-1 px-4 rounded-[10px] transition-all duration-1000 ease-in-out
  ${isAvatarSubmitted ? "opacity-100 pointer-events-auto border border-black/25 shadow-[0_4px_10px_rgba(0,0,0,0.1)] pb-4" : ""}
  ${!isReturningUser ? "opacity-20 pointer-events-none" : ""}
  ${errors.ageError && currentSection === "age" ? "border-[1.5px] border-red-500" : ""}`;

  let ageWrapperSelectedStyle = {};
  if (selectedAge) {
    ageWrapperSelectedStyle = { border: "none", boxShadow: "none" };
  }

  return (
    <div className="max-w-[480px] mx-auto text-center max-[750px]:w-[90%] max-[750px]:mx-auto">
      <header className="py-1 px-4 text-left">
        <h1 className="m-0 text-[2rem] relative max-[750px]:text-[2rem]">{t("Heading")}</h1>
        <p className="m-0 font-poppins text-[#6c757d] text-[1.2rem]">{t("SubHeading")}</p>
      </header>
      <form onSubmit={handleSubmit}>
        <div
          className={`flex flex-col py-1 px-4 rounded-[10px] transition-all duration-1000 ease-in-out ${!isNameSubmitted && !isReturningUser ? "opacity-100 pointer-events-auto border border-black/25 shadow-[0_4px_10px_rgba(0,0,0,0.1)] pb-4" : ""}`}
        >
          <label
            htmlFor="name"
            className="text-left text-[#6c757d] text-[1.1rem] font-poppins mb-[0.3rem]"
            style={{ color: errors.nameError && currentSection === "name" ? "red" : "" }}
          >
            {t("NameLabel")}
          </label>
          <input
            id="name"
            type="text"
            placeholder={t("NameInputPlaceholder")}
            onChange={handleNameInput}
            onFocus={handleNameFocus}
            onBlur={handleNameBlur}
            value={name}
            className={`rounded-[0.8rem] p-4 text-2xl focus:outline-none placeholder:text-[#6c757d] placeholder:font-poppins ${borderClass}`}
          />
        </div>

        <div className={avatarWrapperClass}>
          <label
            htmlFor="avatar"
            className="text-left text-[#6c757d] text-[1.1rem] font-poppins mb-[0.3rem]"
            style={{
              color: errors.avatarSelectedError && currentSection === "avatar" ? "red" : "",
            }}
          >
            {t("AvatarLabel")}
          </label>
          <div className="flex">
            <div className="w-[35%] border border-green-500 flex justify-center items-center flex-col relative">
              <Image
                className="absolute bottom-[15px] z-[2] !w-[100px] !h-[100px]"
                src={
                  !selectedAvatar.imagePath
                    ? "/images/webGl/avatar/emptyAvatar.webp"
                    : selectedAvatar.imagePath
                }
                width={125}
                height={125}
                alt="Selected Avatar"
              />
              <Image
                className="absolute bottom-0"
                src="/images/webGl/avatar/Platform.webp"
                width={106}
                height={30}
                alt="Platform"
              />
            </div>
            <div className="w-[65%] flex flex-col gap-[10px] overflow-x-auto scrollbar-none">
              <div className="flex gap-[10px]">
                {avatarsInfo.slice(0, 10).map((avatar) => (
                  <div
                    style={{ display: "flex", justifyContent: "center", alignItems: "center" }}
                    key={avatar.index}
                    onClick={() => handleAvatarSelect(avatar)}
                    className={`bg-[#dedede66] p-[0.4rem] cursor-pointer ${
                      selectedAvatar.imagePath === avatar.imagePath ? "!bg-white border border-[#9258fe] rounded-[0.8rem]" : ""
                    }`}
                  >
                    <Image src={avatar.imagePath} width={62} height={62} alt={avatar.name} className="!w-[50px] !h-[50px]" />
                  </div>
                ))}
              </div>
              <div className="flex gap-[10px]">
                {avatarsInfo.slice(10, 20).map((avatar) => (
                  <div
                    style={{ display: "flex", justifyContent: "center", alignItems: "center" }}
                    key={avatar.index}
                    onClick={() => handleAvatarSelect(avatar)}
                    className={`bg-[#dedede66] p-[0.4rem] cursor-pointer ${
                      selectedAvatar.imagePath === avatar.imagePath ? "!bg-white border border-[#9258fe] rounded-[0.8rem]" : ""
                    }`}
                  >
                    <Image src={avatar.imagePath} width={62} height={62} alt={avatar.name} className="!w-[50px] !h-[50px]" />
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        <div className={ageWrapperClass} style={{ ...ageWrapperSelectedStyle }}>
          <label
            htmlFor="age"
            className="text-left text-[#6c757d] text-[1.1rem] font-poppins mb-[0.3rem]"
            style={{ color: errors.ageError && currentSection === "age" ? "red" : "" }}
          >
            {t("AgeLabel")}
          </label>
          <div className="flex flex-col gap-[10px] w-full">
            <div className="flex gap-[10px] justify-center max-[750px]:gap-[7px]">
              {[2, 3, 4, 5, 6].map((age) => (
                <div
                  key={age}
                  onClick={() => handleAgeClick(age)}
                  className={`p-2 text-[1.3rem] rounded-2xl text-[#9258fe] border border-[#cbb1fc] cursor-pointer w-14 h-[1.8rem] flex items-center justify-center hover:bg-[#cbb1fc] max-[750px]:p-[0.6rem] max-[750px]:text-[1.3rem] max-[750px]:w-12 max-[750px]:h-12 ${selectedAge === age ? "bg-[#cbb1fc]" : ""}`}
                >
                  {age}
                </div>
              ))}
            </div>
            <div className="flex gap-[10px] justify-center max-[750px]:gap-[7px]">
              {[7, 8, 9, 10].map((age) => (
                <div
                  key={age}
                  onClick={() => handleAgeClick(age)}
                  className={`p-2 text-[1.3rem] rounded-2xl text-[#9258fe] border border-[#cbb1fc] cursor-pointer w-14 h-[1.8rem] flex items-center justify-center hover:bg-[#cbb1fc] max-[750px]:p-[0.6rem] max-[750px]:text-[1.3rem] max-[750px]:w-12 max-[750px]:h-12 ${selectedAge === age ? "bg-[#cbb1fc]" : ""}`}
                >
                  {age}
                </div>
              ))}
            </div>
          </div>
        </div>

        {(errors.nameError || errors.ageError || errors.avatarSelectedError) && (
          <div className="w-3/4 rounded-[5px] bg-[#f8d7ce] font-nevermind-light text-[#dd4a38] mt-2 py-2 px-0 mx-auto">
            <p className="font-nevermind-medium m-0">
              <Image src="/images/webGl/warning.png" height={15} width={15} alt="Warning" />{" "}
              {t("ErrorMsgAll")}
            </p>
          </div>
        )}
        <FormCtaButton text={t("CtaBtn")} />
      </form>
      <p className="text-base mt-4 mb-0 text-[#6c757d] font-poppins cursor-pointer" onClick={() => setShowPopup((prev) => !prev)}>
        <Image src="/images/webGl/normalInfo.png" height={15} width={15} alt="Info" />{" "}
        {t("InfoText")}
      </p>
      {showPopup && (
        <InfoPopup
          isOpen={showPopup}
          onClose={() => setShowPopup((prev) => !prev)}
          data={inforPopupData[0]}
          localeData="CreatePlayer"
        />
      )}
    </div>
  );
};

export default CreatePlayerSection;
