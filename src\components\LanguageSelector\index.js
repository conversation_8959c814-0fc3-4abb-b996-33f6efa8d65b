"use client";

import setLanguageValue from "@/actions/set-language";
import Cookies from "js-cookie";
import { useTranslations } from "next-intl";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
// import styles from "./styles.module.css";

const LanguageSelector = () => {
  const router = useRouter();
  const t = useTranslations("languageSelector");
  const [isOptionsVisible, setIsOptionsVisible] = useState(false);
  const [locale, setLocale] = useState("en");

  useEffect(() => {
    // Set the initial locale if available in cookies (only on the client side)
    const savedLocale = Cookies.get("locale") || "en";
    setLocale(savedLocale);
  }, []);

  // Handle toggle for language options visibility
  const toggleLanguageOptions = () => {
    setIsOptionsVisible((prev) => !prev);
  };

  const handleLanguageChange = (lang) => {
    setLanguageValue(lang);
    setIsOptionsVisible(false);
    setLocale(lang);

    // Save language preference in cookies
    Cookies.set("locale", lang, { expires: 365 });

    window.scrollTo({ top: 0, behavior: "smooth" });

    if (router && router.push) {
      router.refresh();
    }
  };

  return (
    <>
      {isOptionsVisible && (
        <div
          className="fixed top-0 left-0 right-0 bottom-0 bg-black bg-opacity-50 z-[1]"
          onClick={() => setIsOptionsVisible(false)}
        ></div>
      )}
      <div
        className="cursor-pointer ml-4 flex gap-[5px] items-center justify-center relative z-[2] max-[820px]:mt-8"
        onClick={toggleLanguageOptions}
      >
        <div>
          <Image
            src="/images/LangTranslatorIcon.webp"
            width={32}
            height={30}
            alt={t("translator_alt")}
            className="max-[820px]:w-[42px] max-[820px]:h-[40px]"
          />
        </div>
        <div>{locale.toUpperCase()}</div>
        <div>
          <Image
            src="/images/SelectMenuIcon.webp"
            width={15}
            height={15}
            alt={t("menu_selector_alt")}
            className="max-[820px]:w-[42px] max-[820px]:h-[40px]"
          />
        </div>
      </div>
      {isOptionsVisible && (
        <div className="card-base fixed top-[90px] right-[40px] shadow-lg z-[3] w-[195px] max-[820px]:top-[30%] max-[820px]:left-1/2 max-[820px]:transform max-[820px]:-translate-x-1/2 max-[820px]:-translate-y-1/2 max-[820px]:w-1/2">
          <ul className="list-none m-0 p-0">
            <li
              className={`text-label py-[15px] px-0 cursor-pointer border-b border-gray-400 text-center last:border-b-0 ${locale === "en" ? "nav-link-active" : ""}`}
              onClick={() => handleLanguageChange("en")}
            >
              English (US)
            </li>
            <li
              className={`text-label py-[15px] px-0 cursor-pointer border-b border-gray-400 text-center last:border-b-0 ${locale === "uk" ? "nav-link-active" : ""}`}
              onClick={() => handleLanguageChange("uk")}
            >
              English (UK)
            </li>
            <li
              className={`text-label py-[15px] px-0 cursor-pointer border-b border-gray-400 text-center last:border-b-0 ${locale === "da" ? "nav-link-active" : ""}`}
              onClick={() => handleLanguageChange("da")}
            >
              Danish
            </li>
            <li
              className={`text-label py-[15px] px-0 cursor-pointer border-b border-gray-400 text-center last:border-b-0 ${locale === "br" ? "nav-link-active" : ""}`}
              onClick={() => handleLanguageChange("br")}
            >
              Portuguese
            </li>
            <li
              className={`text-label py-[15px] px-0 cursor-pointer border-b border-gray-400 text-center last:border-b-0 ${locale === "nb" ? "nav-link-active" : ""}`}
              onClick={() => handleLanguageChange("nb")}
            >
              Norwegian
            </li>
            <li
              className={`text-label py-[15px] px-0 cursor-pointer border-b border-gray-400 text-center last:border-b-0 ${locale === "sv" ? "nav-link-active" : ""}`}
              onClick={() => handleLanguageChange("sv")}
            >
              Swedish
            </li>
          </ul>
        </div>
      )}
    </>
  );
};

export default LanguageSelector;
