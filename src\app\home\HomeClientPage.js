"use client";
import { LandingReview } from "@/constants";
import useScrollAnimation from "@/hooks/useScrollAnimation";
import { gsap } from "gsap";
import { ScrollToPlugin } from "gsap/ScrollToPlugin";
import { ScrollTrigger } from "gsap/dist/ScrollTrigger";
import { useTranslations } from "next-intl";
import Image from "next/image";
import Carousel from "../../components/CarouselHome";
import HomeThemeCarouselCt from "./HomeThemeCarouselCt";

gsap.registerPlugin(ScrollToPlugin);
gsap.registerPlugin(ScrollTrigger);

const HomeClientPage = () => {
  const t = useTranslations("HomePage");

  const setRef = useScrollAnimation(
    {
      duration: 1,
      ease: "power3.out",
      start: "top bottom",
    },
    []
  );

  return (
    <>
      <div className="max-w-[1728px] mx-auto bg-[#f7f0ea]">
        <Carousel />
        <div className="bg-[url('/images/homeNurturingBgMb.png')] bg-cover bg-center bg-no-repeat md:bg-[url('/images/homeNurturingBg.png')]">
          <h2
            className="text-[1.2rem] md:text-[3.5rem] font-nevermind-bold font-bold py-12 text-center text-[#6828ee] px-[0.2rem] box-border"
            ref={(el) => setRef(el)}
          >
            {t("nurturingGrowth.title.part1")}{" "}
            <span className="inline-block align-middle bg-[#ffc3ff] rounded-[100px] px-[0.5rem] py-[0.2rem] md:px-[1.2rem] md:py-[0.3rem]">
              {t("nurturingGrowth.title.highlight")}{" "}
              <Image
                src="/images/homeArrow.png"
                width={60}
                height={60}
                className="inline-block align-top w-[25px] h-[25px] md:w-[52px] md:h-[52px] md:relative md:top-[14px] md:-left-[10px]"
                alt="Arrow"
              />
            </span>{" "}
            {t("nurturingGrowth.title.part2")}
            <br />
            {t("nurturingGrowth.title.part3")}
          </h2>
          <div className="flex flex-col items-center flex-wrap md:px-12 md:flex-row md:justify-center">
            <div className="max-w-[360px] py-8 px-0 mb-8 flex justify-center items-center flex-col rounded-[20px] mx-4 bg-[#1dc368]">
              <Image
                src="/images/homeNurturing1.png"
                width={320}
                height={291}
                alt={t("nurturingGrowth.cards.card1")}
                className="w-[252px] h-[240px] md:w-[320px] md:h-[291px]"
              />
              <p className="text-[1.5rem] md:text-[2.5rem] font-nevermind-bold font-bold text-center text-white m-0 box-border">
                {t("nurturingGrowth.cards.card1")}
              </p>
            </div>
            <div className="max-w-[360px] py-8 px-0 mb-8 flex justify-center items-center flex-col rounded-[20px] mx-4 bg-[#1dc368]">
              <Image
                src="/images/homeNurturing2.png"
                alt={t("nurturingGrowth.cards.card2")}
                width={320}
                height={291}
                className="w-[252px] h-[240px] md:w-[320px] md:h-[291px]"
              />
              <p className="text-[1.5rem] md:text-[2.5rem] font-nevermind-bold font-bold text-center text-white m-0 box-border">
                {t("nurturingGrowth.cards.card2")}
              </p>
            </div>
            <div className="max-w-[360px] py-8 px-0 mb-8 flex justify-center items-center flex-col rounded-[20px] mx-4 bg-[#1dc368]">
              <Image
                src="/images/homeNurturing3.png"
                width={320}
                height={291}
                alt={t("nurturingGrowth.cards.card3")}
                className="w-[252px] h-[240px] md:w-[320px] md:h-[291px]"
              />
              <p className="text-[1.5rem] md:text-[2.5rem] font-nevermind-bold font-bold text-center text-white m-0 box-border">
                {t("nurturingGrowth.cards.card3")}
              </p>
            </div>
          </div>
        </div>
        <div ref={(el) => setRef(el)}>
          <h2 className="text-[1.2rem] md:text-[3.5rem] font-nevermind-bold font-bold text-center text-[#6828ee] px-[0.2rem] box-border">
            {t("discoverThemes.title.part1")} <br />
            <span className="inline-block align-middle bg-[#ffc3ff] rounded-[100px] px-[0.5rem] py-[0.2rem] md:px-[1.2rem] md:py-[0.3rem]">
              {t("discoverThemes.title.highlight1")}{" "}
              <Image
                src="/images/homeHat.png"
                alt="Hat"
                width={60}
                height={60}
                className="inline-block align-top w-[25px] h-[25px] md:w-[52px] md:h-[52px] md:relative md:top-[14px] md:-left-[10px]"
              />
            </span>{" "}
            {t("discoverThemes.title.and")}{" "}
            <span className="inline-block align-middle bg-[#ffc3ff] rounded-[100px] px-[0.5rem] py-[0.2rem] md:px-[1.2rem] md:py-[0.3rem]">
              {t("discoverThemes.title.highlight2")}{" "}
              <Image
                src="/images/homeRemote.png"
                alt="Remote"
                width={60}
                height={60}
                className="inline-block align-top w-[25px] h-[25px] md:w-[52px] md:h-[52px] md:relative md:top-[14px] md:-left-[10px]"
              />
            </span>{" "}
          </h2>
          <HomeThemeCarouselCt />

          <div className="bg-[#955dff] bg-cover bg-no-repeat pt-2">
            <h2
              className="text-[3rem] md:text-[4.5rem] font-nevermind-bold font-bold text-center text-white px-[0.2rem] box-border"
              ref={(el) => setRef(el)}
            >
              {t("reviews.title")}
            </h2>
            <div className="flex justify-start gap-2 my-4 mx-0 text-center overflow-auto scrollbar-none snap-x snap-mandatory">
              {LandingReview.map((item, index) => (
                <div
                  key={index}
                  className="pt-4 pr-0 pb-0 pl-0 rounded-lg w-[300px] bg-[#f7f8fe] flex flex-col items-center justify-between font-poppins text-[#6828ee] mx-[10px]"
                  ref={(el) => setRef(el)}
                >
                  <div>
                    <Image src={item.starImg} width={152} height={24} alt="Rating Stars" />
                    <p className="text-base my-4 mx-8 mb-2">{item.content}</p>
                  </div>
                  <div className="bg-white w-[300px] rounded-b-lg border-t border-[#e0e0e0] pt-4 mt-4 text-sm">
                    <p className="text-sm text-[#666] m-0 pb-4">{item.description}</p>
                  </div>
                </div>
              ))}
            </div>

            <div className="mx-4 lg:mx-8">
              <div
                className="flex justify-around flex-col lg:flex-row lg:mx-8 lg:my-4"
                ref={(el) => setRef(el)}
              >
                <div className="text-[3rem] text-white text-center lg:text-[9rem]">
                  {t("socialProof.impactMade")}
                </div>
                <div className="bg-white/40 backdrop-blur-[5px] rounded-lg border-[0.5px] border-white flex items-center flex-col w-[99%] my-4 lg:w-[47%] lg:justify-between">
                  <p className="text-[2rem] text-white">{t("socialProof.problemsSolved")}</p>
                  <div className="relative w-[60%] h-0 pb-[50%] m-0">
                    <Image
                      alt="Social Proof Learners"
                      src="/images/socialProofSkiils.webp"
                      layout="fill"
                      objectFit="contain"
                    />
                  </div>
                </div>
              </div>
              <div className="flex justify-around flex-col lg:flex-row lg:mx-8 lg:my-0">
                <div className="flex items-center bg-white/40 backdrop-blur-[5px] rounded-lg border-[0.5px] border-white text-[1.5rem] text-white my-4 lg:w-[47%] lg:flex-row lg:text-[2rem] lg:my-0">
                  <div className="relative w-[60%] h-0 pb-[50%] m-0">
                    <Image
                      alt="Social Proof Learners"
                      src="/images/socialProofLearners.webp"
                      layout="fill"
                      objectFit="contain"
                    />
                  </div>
                  <div className="w-[40%] pl-[10px] text-center">
                    <p className="mx-auto text-center">
                      {t("socialProof.happyLearners.title")}
                      <br />
                      {t("socialProof.happyLearners.subtitle")}
                    </p>
                  </div>
                </div>
                <div className="flex items-center bg-white/40 backdrop-blur-[5px] rounded-lg border-[0.5px] border-white text-[1.5rem] text-white my-4 lg:w-[47%] lg:flex-row lg:text-[2rem] lg:my-0">
                  <div className="relative w-[60%] h-0 pb-[50%] m-0">
                    <Image
                      alt="Social Proof Rating"
                      src="/images/socialProofRating.png"
                      layout="fill"
                      objectFit="contain"
                    />
                  </div>
                  <div className="w-[40%] pl-[10px] text-center">
                    <p className="mx-auto text-center">
                      {t("socialProof.rating.title")}
                      <br />
                      <Image
                        src="/images/4.5_star_white.png"
                        width={100}
                        height={20}
                        alt="Rating Stars"
                      />
                    </p>
                  </div>
                </div>
              </div>
            </div>
            <div className="flex flex-col mx-4 my-0 justify-center items-center text-white text-center pb-12 gap-5 lg:flex-col lg:justify-around lg:mx-8">
              <div ref={(el) => setRef(el)}>
                <h2 className="text-[3rem] md:text-[4.5rem] font-nevermind-bold font-bold text-white">
                  {t("awards.title")}
                </h2>
              </div>
              <div className="awards-grid-home">
                <Image
                  src="/images/Award1.webp"
                  height={120}
                  width={120}
                  alt="Award"
                  className="award-image-home"
                  ref={(el) => setRef(el)}
                />
                <Image
                  src="/images/Award2.webp"
                  height={120}
                  width={120}
                  alt="Award"
                  className="award-image-home"
                  ref={(el) => setRef(el)}
                />
                <Image
                  src="/images/Award3.webp"
                  height={120}
                  width={120}
                  alt="Award"
                  className="award-image-home"
                  ref={(el) => setRef(el)}
                />
                <Image
                  src="/images/Award4.webp"
                  height={120}
                  width={120}
                  alt="Award"
                  className="award-image-home"
                  ref={(el) => setRef(el)}
                />
                <Image
                  src="/images/Award5.webp"
                  height={120}
                  width={120}
                  alt="Award"
                  className="award-image-home"
                  ref={(el) => setRef(el)}
                />
                <Image
                  src="/images/Award6.webp"
                  height={120}
                  width={120}
                  alt="Award"
                  className="award-image-home"
                  ref={(el) => setRef(el)}
                />
                <Image
                  src="/images/Award7.png"
                  height={120}
                  width={120}
                  alt="Award"
                  className="award-image-home"
                  ref={(el) => setRef(el)}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default HomeClientPage;
