"use client";

import { useAuth } from "@/context/AuthContext";
import useIsMobile from "@/hooks/useIsMobile";
import { trackWebEngageEvent } from "@/utils/webengage";
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import { useTranslations } from "next-intl";
import Image from "next/image";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import { useEffect, useRef, useState } from "react";
// import styles from "./styles.module.css";

gsap.registerPlugin(ScrollTrigger);

const Footer = () => {
  const isMobile = useIsMobile();
  const scrollRef = useRef(null);
  const lockContentRef = useRef(null);
  const lockIconRef = useRef(null);
  const [isUnlocked, setIsUnlocked] = useState(false);
  const router = useRouter();
  const { isLoggedIn, isSubscribed } = useAuth();
  const pathname = usePathname();

  const t = useTranslations("Footer");

  const scrollToTop = (path) => {
    if (window.location.pathname === path) {
      window.scrollTo({ top: 0, behavior: "smooth" });
    }
  };

  const handleRedirect = () => {
    router.push("/acquisition");
  };

  const specificPages = ["/user-home-screen/", "/game-player/"];
  const isSpecificPage = specificPages.includes(pathname);

  const handleWebIcon = () => {
    trackWebEngageEvent("WebGLFooterIconClk");

    if (isSubscribed) {
      router.push("/user-home-screen");
    } else {
      router.push("/get-started");
    }
  };

  useEffect(() => {
    gsap.set(scrollRef.current, { y: 100, opacity: 0 });
    gsap.set(lockContentRef.current, { y: 50 });
    ScrollTrigger.create({
      trigger: lockContentRef.current,
      start: "top center",
      onEnter: () => {
        gsap.to(lockContentRef.current, { y: -50, duration: 0.5 });
        gsap.to(scrollRef.current, {
          y: 0,
          opacity: 1,
          duration: 2,
          delay: 0.5,
        });
        gsap.to(lockIconRef.current, {
          duration: 0.5,
          onComplete: () => {
            setIsUnlocked(true);
            gsap.to(lockIconRef.current, { opacity: 1, duration: 0.5 });
          },
        });
      },
    });
  }, []);

  return (
    <>
      <div
        className={`footer-wrapper ${isSpecificPage ? "max-[500px]:landscape:hidden" : ""}`}
        id="footer"
      >
        {/* Pink Footer Section - Only show if not logged in or not subscribed */}
        {!isLoggedIn || (isLoggedIn && !isSubscribed) ? (
          <div className="pink-footer-wrapper" id="pinkFooterContainer">
            <div ref={lockContentRef} className="lock-content-wrapper">
              <p ref={lockIconRef}>
                {isUnlocked ? (
                  <Image src="/images/unlock.png" alt="Unlock Icon" width={96} height={96} />
                ) : (
                  <Image src="/images/lock.png" alt="Lock Icon" width={96} height={96} />
                )}
              </p>
              <p className="heading-secondary">{t("UnlockGrowth")}</p>
              <button className="footer-cta-button" onClick={handleRedirect}>
                {t("UnlockNow")}
              </button>
            </div>
            {/* Game Cards Section */}
            <div ref={scrollRef} className="game-cards-wrapper">
              <div className="game-cards-section">
                <div className="game-card-col1">
                  <div>
                    <Image
                      src="/images/pinkGameCard1.webp"
                      width={isMobile ? 166 : 250}
                      height={isMobile ? 271 : 379}
                      className="object-fill"
                      objectFit="contain"
                      alt={t("LearnToRead")}
                      placeholder="blur"
                      blurDataURL="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNctmhZPQAGUwJv7ObBEQAAAABJRU5ErkJggg=="
                    />
                    <p>{t("LearnToRead")}</p>
                  </div>
                </div>
                <div className="game-card-col2">
                  <div>
                    <Image
                      src="/images/pinkGameCard2.webp"
                      width={isMobile ? 166 : 250}
                      height={isMobile ? 72 : 100}
                      className="object-fill"
                      objectFit="cover"
                      alt={t("Tracing")}
                    />
                    <p>{t("Tracing")}</p>
                  </div>
                  <div>
                    <Image
                      src="/images/pinkGameCard3.webp"
                      width={isMobile ? 166 : 250}
                      height={isMobile ? 165 : 238}
                      className="object-fill"
                      alt={t("Fitness")}
                    />
                    <p>{t("Fitness")}</p>
                  </div>
                </div>
              </div>
              <div className="game-cards-section">
                <div className="game-card-col2">
                  <div>
                    <Image
                      src="/images/pinkGameCard7.webp"
                      width={isMobile ? 166 : 250}
                      height={isMobile ? 122 : 174}
                      className="object-fill"
                      objectFit="cover"
                      alt={t("Activities")}
                    />
                    <p>{t("Activities")}</p>
                  </div>
                  <div>
                    <Image
                      src="/images/pinkGameCard5.webp"
                      width={isMobile ? 166 : 250}
                      height={isMobile ? 104 : 158}
                      className="object-fill"
                      alt={t("EmotionalWellbeing")}
                    />
                    <p>{t("EmotionalWellbeing")}</p>
                  </div>
                </div>
                <div className="game-card-col2">
                  <div>
                    <Image
                      src="/images/pinkGameCard6.webp"
                      width={isMobile ? 166 : 250}
                      height={isMobile ? 72 : 98}
                      className="object-fill"
                      alt={t("Math")}
                    />
                    <p>{t("Math")}</p>
                  </div>
                  <div>
                    <Image
                      src="/images/pinkGameCard4.webp"
                      width={isMobile ? 166 : 250}
                      height={isMobile ? 165 : 238}
                      className="object-fill"
                      alt={t("Avatars")}
                    />
                    <p>{t("Avatars")}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        ) : null}

        {/* Green Footer Section */}
        <div className="footer-green-wrapper">
          {/* Store Icons */}
          <div className="store-icons-wrapper">
            <button
              onClick={() => window.open("https://www.amazon.com/gp/product/B0DH6RT2JV", "_blank")}
              className="store-icons"
              aria-label="Download from Amazon"
            >
              <Image
                src="/images/footer/socialIcons/Amazon.webp"
                width={198}
                height={60}
                alt={t("Amazon")}
              />
            </button>
            <button
              onClick={() =>
                window.open(
                  "https://apps.apple.com/us/app/skidos-learning-games-for-kids/id1483744837",
                  "_blank"
                )
              }
              className="store-icons"
              aria-label="Download from App Store"
            >
              <Image
                src="/images/footer/socialIcons/Appstore.webp"
                width={198}
                height={60}
                alt={t("Appstore")}
              />
            </button>
            <button
              onClick={() =>
                window.open(
                  "https://play.google.com/store/apps/details?id=skidos.shopping.toddler.learning.games&hl=en_IN",
                  "_blank"
                )
              }
              className="store-icons"
              aria-label="Download from Play Store"
            >
              <Image
                src="/images/footer/socialIcons/Playstore.webp"
                width={198}
                height={60}
                alt={t("Playstore")}
              />
            </button>
            <button onClick={handleWebIcon} className="store-icons" aria-label="Visit Web App">
              <Image
                src="/images/footer/socialIcons/Web.webp"
                width={198}
                height={60}
                alt={t("Web")}
              />
            </button>
          </div>

          {/* Footer Content */}
          <div className="footer-content-wrapper">
            <div className="footer-content-left">
              <div>
                <Link href="/" className="noLinkStyle">
                  <Image src="/images/skidosLogo.png" width={167} height={52} alt={t("LogoAlt")} />
                </Link>
              </div>
              <div className="footer-content-left-img">
                <Link
                  href="https://www.instagram.com/skidoslearning/"
                  className="noLinkStyle"
                  prefetch={false}
                >
                  <Image
                    src="/images/footer/socialIcons/Insta.png"
                    width={40}
                    height={40}
                    alt="Instagram"
                    className="cursor-pointer hover:scale-110 transition-transform duration-300"
                  />
                </Link>
                <Link
                  href="https://www.tiktok.com/@skidoslearning"
                  className="noLinkStyle"
                  prefetch={false}
                >
                  <Image
                    src="/images/footer/socialIcons/TiktokIcon.png"
                    width={40}
                    height={40}
                    alt="TikTok"
                    className="cursor-pointer hover:scale-110 transition-transform duration-300"
                  />
                </Link>
                <Link
                  href="https://www.youtube.com/user/playatskidos"
                  className="noLinkStyle"
                  prefetch={false}
                >
                  <Image
                    src="/images/footer/socialIcons/Youtube.png"
                    width={40}
                    height={40}
                    alt="Youtube"
                    className="cursor-pointer hover:scale-110 transition-transform duration-300"
                  />
                </Link>
                <Link
                  href="https://dk.linkedin.com/company/skidos-games"
                  className="noLinkStyle"
                  prefetch={false}
                >
                  <Image
                    src="/images/footer/socialIcons/Linkedin.png"
                    width={40}
                    height={40}
                    alt="Linkedin"
                    className="cursor-pointer hover:scale-110 transition-transform duration-300"
                  />
                </Link>
              </div>
            </div>
            <div className="footer-content-right">
              <div className="footer-content-right-top">
                <div>
                  <h2 className="heading-quaternary  py-4">{t("AboutUs")}</h2>
                  <ul>
                    <li>
                      <Link
                        href="/blogs"
                        className="footer-link-hoverable"
                        prefetch={false}
                        onClick={() => scrollToTop("/blogs/")}
                      >
                        {t("Blogs")}
                      </Link>
                    </li>
                    <li>
                      <Link
                        href="/news"
                        className="footer-link-hoverable"
                        prefetch={false}
                        onClick={() => scrollToTop("/news/")}
                      >
                        {t("News")}
                      </Link>
                    </li>
                    <li>
                      <Link
                        href="/career"
                        className="footer-link-hoverable"
                        prefetch={false}
                        onClick={() => scrollToTop("/career/")}
                      >
                        {t("Careers")}
                      </Link>
                    </li>
                  </ul>
                </div>
                <div>
                  <h2 className="heading-quaternary  py-4">{t("Support")}</h2>
                  <ul>
                    <li>
                      <Link
                        href="/terms"
                        className="footer-link-hoverable"
                        prefetch={false}
                        onClick={() => scrollToTop("/terms/")}
                      >
                        {t("TermsAndConditions")}
                      </Link>
                    </li>
                    <li>
                      <Link
                        href="/privacy-policy"
                        className="footer-link-hoverable"
                        prefetch={false}
                        onClick={() => scrollToTop("/privacy-policy/")}
                      >
                        {t("PrivacyPolicy")}
                      </Link>
                    </li>
                    <li>
                      <Link
                        href="https://support.skidos.com/support/home"
                        className="footer-link-hoverable"
                        prefetch={false}
                      >
                        {t("FAQ")}
                      </Link>
                    </li>
                    <li>
                      <Link
                        href="/partnership"
                        className="footer-link-hoverable"
                        prefetch={false}
                        onClick={() => scrollToTop("/partnership/")}
                      >
                        {t("Partnership")}
                      </Link>
                    </li>
                  </ul>
                </div>
                <div>
                  <h2 className="heading-quaternary  py-4">{t("Contact")}</h2>
                  <ul>
                    <li className="text-sm">
                      Skidos Labs ApS,
                      <br />
                      Titangade 11
                      <br />
                      2200 København N<br />
                      CVR: 37212962
                    </li>
                    <li>
                      <a
                        className="footer-link-hoverable"
                        href="mailto:<EMAIL>"
                        target="_blank"
                        rel="noopener noreferrer"
                        data-stringify-link="mailto:<EMAIL>"
                        data-sk="tooltip_parent"
                        aria-haspopup="menu"
                        aria-expanded="false"
                      >
                        <EMAIL>
                      </a>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default Footer;
