"use client";

import FormCtaButton from "@/components/FormCtaButton";
import { useAuth } from "@/context/AuthContext";
import useIsMobile from "@/hooks/useIsMobile";
import { trackWebEngageEvent } from "@/utils/webengage";
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import { useTranslations } from "next-intl";
import Image from "next/image";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import { useEffect, useRef, useState } from "react";
// import styles from "./styles.module.css";

gsap.registerPlugin(ScrollTrigger);

const Footer = () => {
  const isMobile = useIsMobile();
  const scrollRef = useRef(null);
  const lockContentRef = useRef(null);
  const lockIconRef = useRef(null);
  const [isUnlocked, setIsUnlocked] = useState(false);
  const router = useRouter();
  const { isLoggedIn, isSubscribed } = useAuth();
  const pathname = usePathname();

  const t = useTranslations("Footer");

  const scrollToTop = (path) => {
    if (window.location.pathname === path) {
      window.scrollTo({ top: 0, behavior: "smooth" });
    }
  };

  const handleRedirect = () => {
    router.push("/acquisition");
  };

  const specificPages = ["/user-home-screen/", "/game-player/"];
  const isSpecificPage = specificPages.includes(pathname);

  const handleWebIcon = () => {
    trackWebEngageEvent("WebGLFooterIconClk");

    if (isSubscribed) {
      router.push("/user-home-screen");
    } else {
      router.push("/get-started");
    }
  };

  useEffect(() => {
    gsap.set(scrollRef.current, { y: 100, opacity: 0 });
    gsap.set(lockContentRef.current, { y: 50 });
    ScrollTrigger.create({
      trigger: lockContentRef.current,
      start: "top center",
      onEnter: () => {
        gsap.to(lockContentRef.current, { y: -50, duration: 0.5 });
        gsap.to(scrollRef.current, {
          y: 0,
          opacity: 1,
          duration: 2,
          delay: 0.5,
        });
        gsap.to(lockIconRef.current, {
          duration: 0.5,
          onComplete: () => {
            setIsUnlocked(true);
            gsap.to(lockIconRef.current, { opacity: 1, duration: 0.5 });
          },
        });
      },
    });
  }, []);

  return (
    <>
      <div
        className={`max-w-container mx-auto ${isSpecificPage ? "max-[500px]:landscape:hidden" : ""}`}
        id="footer"
      >
        {!isLoggedIn || (isLoggedIn && !isSubscribed) ? (
          <div
            className="bg-[url('/images/pinkBg.webp')] bg-cover bg-no-repeat bg-center w-full pt-[8.3rem] pb-6"
            id="pinkFooterContainer"
          >
            <div ref={lockContentRef} className="flex items-center flex-col">
              <p ref={lockIconRef} className="m-0">
                {isUnlocked ? (
                  <Image src="/images/unlock.png" alt="Unlock Icon" width={96} height={96} />
                ) : (
                  <Image src="/images/lock.png" alt="Lock Icon" width={96} height={96} />
                )}
              </p>
              <p className="heading-secondary text-white my-4 mx-0 md:mb-8">{t("UnlockGrowth")}</p>
              <FormCtaButton text={t("UnlockNow")} onClick={handleRedirect} customStyles="mt-0" />
            </div>
            <div
              ref={scrollRef}
              className="flex gap-[10px] justify-center text-center mt-8 mx-8 mb-0 md:gap-5"
            >
              <div className="flex gap-[10px] flex-wrap justify-end gap-5">
                <div>
                  <div className="bg-white p-[5px] rounded-xl md:p-[3px]">
                    <Image
                      src="/images/pinkGameCard1.webp"
                      width={isMobile ? 166 : 250}
                      height={isMobile ? 271 : 379}
                      className="object-fill"
                      objectFit="contain"
                      alt={t("LearnToRead")}
                      placeholder="blur"
                      blurDataURL="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNctmhZPQAGUwJv7ObBEQAAAABJRU5ErkJggg=="
                    />
                    <p className="m-0 text-xs md:text-base">{t("LearnToRead")}</p>
                  </div>
                </div>
                <div className="flex flex-col justify-between gap-[15px]">
                  <div className="bg-white p-[5px] rounded-xl md:p-[3px]">
                    <Image
                      src="/images/pinkGameCard2.webp"
                      width={isMobile ? 166 : 250}
                      height={isMobile ? 72 : 100}
                      className="object-fill"
                      objectFit="cover"
                      alt={t("Tracing")}
                    />
                    <p className="m-0 text-xs md:text-base">{t("Tracing")}</p>
                  </div>
                  <div className="bg-white p-[5px] rounded-xl md:p-[3px]">
                    <Image
                      src="/images/pinkGameCard3.webp"
                      width={isMobile ? 166 : 250}
                      height={isMobile ? 165 : 238}
                      className="object-fill"
                      alt={t("Fitness")}
                    />
                    <p className="m-0 text-xs md:text-base">{t("Fitness")}</p>
                  </div>
                </div>
              </div>
              <div className="flex gap-[10px] flex-wrap justify-end gap-5">
                <div className="flex flex-col justify-between gap-[15px]">
                  <div className="bg-white p-[5px] rounded-xl md:p-[3px]">
                    <Image
                      src="/images/pinkGameCard7.webp"
                      width={isMobile ? 166 : 250}
                      height={isMobile ? 122 : 174}
                      className="object-fill"
                      objectFit="cover"
                      alt={t("Activities")}
                    />
                    <p className="m-0 text-xs md:text-base">{t("Activities")}</p>
                  </div>
                  <div className="bg-white p-[5px] rounded-xl md:p-[3px]">
                    <Image
                      src="/images/pinkGameCard5.webp"
                      width={isMobile ? 166 : 250}
                      height={isMobile ? 104 : 158}
                      className="object-fill"
                      alt={t("EmotionalWellbeing")}
                    />
                    <p className="m-0 text-xs md:text-base">{t("EmotionalWellbeing")}</p>
                  </div>
                </div>
                <div className="flex flex-col justify-between gap-[15px]">
                  <div className="bg-white p-[5px] rounded-xl md:p-[3px]">
                    <Image
                      src="/images/pinkGameCard6.webp"
                      width={isMobile ? 166 : 250}
                      height={isMobile ? 72 : 98}
                      className="object-fill"
                      alt={t("Math")}
                    />
                    <p className="m-0 text-xs md:text-base">{t("Math")}</p>
                  </div>
                  <div className="bg-white p-[5px] rounded-xl md:p-[3px]">
                    <Image
                      src="/images/pinkGameCard4.webp"
                      width={isMobile ? 166 : 250}
                      height={isMobile ? 165 : 238}
                      className="object-fill"
                      alt={t("Avatars")}
                    />
                    <p className="m-0 text-xs md:text-base">{t("Avatars")}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        ) : null}
        <div className="bg-secondary-500 w-full pt-12 pb-6">
          <div className="flex justify-center items-center gap-4 flex-wrap mb-8">
            <div
              onClick={() => window.open("https://www.amazon.com/gp/product/B0DH6RT2JV", "_blank")}
            >
              <Image
                src="/images/footer/socialIcons/Amazon.webp"
                width={198}
                height={60}
                alt={t("Amazon")}
                className="cursor-pointer hover:scale-105 transition-transform duration-300"
              />
            </div>
            <div
              onClick={() =>
                window.open(
                  "https://apps.apple.com/us/app/skidos-learning-games-for-kids/id1483744837",
                  "_blank"
                )
              }
            >
              <Image
                src="/images/footer/socialIcons/Appstore.webp"
                width={198}
                height={60}
                alt={t("Appstore")}
                className="cursor-pointer hover:scale-105 transition-transform duration-300"
              />
            </div>
            <div
              onClick={() =>
                window.open(
                  "https://play.google.com/store/apps/details?id=skidos.shopping.toddler.learning.games&hl=en_IN",
                  "_blank"
                )
              }
            >
              <Image
                src="/images/footer/socialIcons/Playstore.webp"
                width={198}
                height={60}
                alt={t("Playstore")}
                className="cursor-pointer hover:scale-105 transition-transform duration-300"
              />
            </div>
            <div onClick={handleWebIcon}>
              <Image
                src="/images/footer/socialIcons/Web.webp"
                width={198}
                height={60}
                alt={t("Web")}
                className="cursor-pointer hover:scale-105 transition-transform duration-300"
              />
            </div>
          </div>
          <div className="flex justify-between items-start flex-wrap gap-8 px-8 max-w-container mx-auto">
            <div className="flex flex-col gap-6">
              <div>
                <Link href="/" className="noLinkStyle">
                  <Image src="/images/skidosLogo.png" width={167} height={52} alt="SKIDOS Logo" />
                </Link>
              </div>
              <div className="flex gap-4">
                <Link
                  href="https://www.instagram.com/skidoslearning/"
                  className="noLinkStyle"
                  prefetch={false}
                >
                  <Image
                    src="/images/footer/socialIcons/Insta.png"
                    width={40}
                    height={40}
                    alt="Instagram"
                  />
                </Link>
                <Link
                  href="https://www.tiktok.com/@skidoslearning"
                  className="noLinkStyle"
                  prefetch={false}
                >
                  <Image
                    src="/images/footer/socialIcons/TiktokIcon.png"
                    width={40}
                    height={40}
                    alt="TikTok"
                  />
                </Link>
                <Link
                  href="https://www.youtube.com/user/playatskidos"
                  className="noLinkStyle"
                  prefetch={false}
                >
                  <Image
                    src="/images/footer/socialIcons/Youtube.png"
                    width={40}
                    height={40}
                    alt="Youtube"
                  />
                </Link>
                <Link
                  href="https://dk.linkedin.com/company/skidos-games"
                  className="noLinkStyle"
                  prefetch={false}
                >
                  <Image
                    src="/images/footer/socialIcons/Linkedin.png"
                    width={40}
                    height={40}
                    alt="Linkedin"
                  />
                </Link>
              </div>
            </div>
            <div className="flex-1 min-w-[300px]">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8 text-white">
                <div>
                  <h2 className="heading-quaternary text-white mb-4">{t("AboutUs")}</h2>
                  <ul className="list-none p-0 space-y-2">
                    <Link
                      href="/blogs"
                      className="noLinkStyle"
                      prefetch={false}
                      onClick={() => scrollToTop("/blogs/")}
                    >
                      <li className="hover:text-gray-300 transition-colors duration-300 cursor-pointer">
                        {t("Blogs")}
                      </li>
                    </Link>
                    <Link
                      href="/news"
                      className="noLinkStyle"
                      prefetch={false}
                      onClick={() => scrollToTop("/news/")}
                    >
                      <li className="hover:text-gray-300 transition-colors duration-300 cursor-pointer">
                        {t("News")}
                      </li>
                    </Link>
                    <Link
                      href="/career"
                      className="noLinkStyle"
                      prefetch={false}
                      onClick={() => scrollToTop("/career/")}
                    >
                      <li className="hover:text-gray-300 transition-colors duration-300 cursor-pointer">
                        {t("Careers")}
                      </li>
                    </Link>
                  </ul>
                </div>
                <div>
                  <h2 className="heading-quaternary text-white mb-4">{t("Support")}</h2>
                  <ul className="list-none p-0 space-y-2">
                    <Link
                      href="/terms"
                      className="noLinkStyle"
                      prefetch={false}
                      onClick={() => scrollToTop("/terms/")}
                    >
                      <li className="hover:text-gray-300 transition-colors duration-300 cursor-pointer">
                        {t("TermsAndConditions")}
                      </li>
                    </Link>
                    <Link
                      href="/privacy-policy"
                      className="noLinkStyle"
                      prefetch={false}
                      onClick={() => scrollToTop("/privacy-policy/")}
                    >
                      <li className="hover:text-gray-300 transition-colors duration-300 cursor-pointer">
                        {t("PrivacyPolicy")}
                      </li>
                    </Link>
                    <Link
                      href="https://support.skidos.com/support/home"
                      className="noLinkStyle"
                      prefetch={false}
                    >
                      <li className="hover:text-gray-300 transition-colors duration-300 cursor-pointer">
                        {t("FAQ")}
                      </li>
                    </Link>
                    <Link
                      href="/partnership"
                      className="noLinkStyle"
                      prefetch={false}
                      onClick={() => scrollToTop("/partnership/")}
                    >
                      <li className="hover:text-gray-300 transition-colors duration-300 cursor-pointer">
                        {t("Partnership")}
                      </li>
                    </Link>
                  </ul>
                </div>
                <div>
                  <h2 className="heading-quaternary text-white mb-4">{t("Contact")}</h2>
                  <ul className="list-none p-0 space-y-2">
                    <li className="text-sm">
                      Skidos Labs ApS,
                      <br />
                      Titangade 11
                      <br />
                      2200 København N<br />
                      CVR: 37212962
                    </li>
                    <li>
                      <a
                        className="noLinkStyle hover:text-gray-300 transition-colors duration-300"
                        href="mailto:<EMAIL>"
                        target="_blank"
                        rel="noopener noreferrer"
                        data-stringify-link="mailto:<EMAIL>"
                        data-sk="tooltip_parent"
                        aria-haspopup="menu"
                        aria-expanded="false"
                        style={{ color: "white" }}
                      >
                        <EMAIL>
                      </a>
                    </li>
                  </ul>
                </div>
              </div>
              <div className="mt-8 pt-4 border-t border-gray-400"></div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default Footer;
