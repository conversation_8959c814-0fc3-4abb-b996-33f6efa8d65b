"use client";
import Loader from "@/components/Loader";
import useScrollAnimation from "@/hooks/useScrollAnimation";
import apiClient from "@/utils/axiosUtil";
import { faSquareUpRight } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { useTranslations } from "next-intl";
import Image from "next/image";
import Link from "next/link";
import { useEffect, useState } from "react";
import Fiks from "../../../public/images/article/fix.png";

const ArticlesClient = ({ articles, articleMedia }) => {
  const [articleData, setArticledata] = useState(articles);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [isLoading, setLoading] = useState(false);
  const [triggerAnimation, setTriggerAnimation] = useState(0);

  const t = useTranslations("News");

  useEffect(() => {
    const getPostData = async () => {
      setLoading(true);
      const url = `${process.env.NEXT_PUBLIC_API_URL}/wp-json/wp/v2/news?per_page=10&page=${page}`;
      const response = await apiClient.get(url);
      const result = response.data;

      if (result.length > 0) {
        setArticledata((prevArticles) => [
          ...prevArticles,
          ...result.filter(
            (newArticle) => !prevArticles.some((article) => article.id === newArticle.id)
          ),
        ]);
        setTriggerAnimation((prev) => prev + 1);
      }

      setLoading(false);
      if (result.length < 10) {
        setHasMore(false);
      }
    };

    getPostData();
  }, [page]);

  const setRef = useScrollAnimation(
    {
      duration: 1,
      ease: "power3.out",
      start: "top bottom",
    },
    [triggerAnimation]
  );

  const formattedDate = (dateStr) => {
    const date = new Date(dateStr);
    const options = { year: "numeric", month: "long", day: "numeric" };
    return new Intl.DateTimeFormat("en-US", options).format(date);
  };

  const loadMoreArticles = () => {
    setPage((prevPage) => prevPage + 1);
  };

  const Heading = ({ content }) => {
    return <h3 dangerouslySetInnerHTML={{ __html: content }} />;
  };

  return (
    <div className="max-w-[1728px] mx-auto">
      <header className="flex justify-center items-center p-8 text-[3rem] gap-5 flex-col md:flex-row md:text-[4rem] md:p-16" ref={(el) => setRef(el)}>
        <h1>{t("title")}</h1> {/* Use translation key */}
        <Image
          src={Fiks}
          className="w-80 h-80"
          alt={t("headerAlt")}
          priority={true}
          quality={85}
        />
      </header>
      {/* <section className={styles.videoContentWrapper} ref={(el) => setRef(el)}>
        <div>
          <iframe
            title={t("videoTitle")}
            className={styles.videoPlyer}
            src="https://www.youtube.com/embed/a7EHVRqeiQI"
          ></iframe>
        </div>
        <div>
          <h1>{t("whatIsSKIDOPass")}</h1>
          <p>{t("passDescription")}</p>
          <button
            className={styles.getPremiumBtn}
            onClick={() =>
              (window.location.href = `${process.env.NEXT_PUBLIC_API_URL}/app/acquisition_home`)
            }
          >
            {t("getPremiumBtn")}
          </button>
        </div>
      </section> */}
      <section ref={(el) => setRef(el)}>
        <h2 className="text-[2rem] pl-4 mb-0 text-center md:pl-12 md:text-left">{t("mediaMentionTitle")}</h2>
        <div className="flex gap-5 my-0 overflow-x-auto scrollbar-none py-8 pr-0 pb-12 pl-4 md:py-8 md:pr-0 md:pb-12 md:pl-12">
          {articleMedia.map((mention, index) => (
            <div key={index} className="w-[300px] bg-[#f9f9f9] font-poppins cursor-pointer flex-shrink-0 border border-[#c8c8c8] rounded-2xl transition-all duration-300 relative group">
              <div
                className="w-full h-[315px] bg-cover bg-center bg-no-repeat rounded-t-2xl flex justify-center items-center relative overflow-hidden before:content-[''] before:absolute before:top-0 before:left-0 before:right-0 before:bottom-0 before:bg-black/0 before:transition-colors before:duration-300 before:z-[1] before:rounded-t-2xl group-hover:before:bg-black/70"
                style={{ backgroundImage: `url(${mention.imageUrl})` }}
              >
                <a
                  href={mention.link}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline p-[0.8rem] bg-[#f9f9f9] rounded-2xl relative z-[2] top-[200px] transition-all duration-300 ease-in-out flex items-center gap-[5px] group-hover:top-0 no-underline text-inherit"
                >
                  {t("read")}{" "}
                  <Image
                    src="/images/article/blackArrow.png"
                    width={20}
                    height={20}
                    alt={t("arrowAlt")}
                  />
                </a>
              </div>
              <p className="p-0 px-2 box-border">{mention.description}</p>
            </div>
          ))}
        </div>
      </section>
      <section>
        <div className="p-0 px-4 pb-4 box-border md:p-0 md:px-12 md:pb-12">
          {articleData.map((item) => (
            <Link
              key={item.id}
              href={{
                pathname: `/news/${item.slug}`,
              }}
              className="no-underline text-inherit"
            >
              <div className="flex border-b border-[#898989] py-4 gap-[10px] font-poppins cursor-pointer flex-col md:flex-row md:gap-[30px] md:py-6" ref={(el) => setRef(el)}>
                <div className="w-full flex items-center md:w-[35%]">
                  <Image
                    alt={item.yoast_head_json?.og_site_name}
                    src={item.yoast_head_json.schema["@graph"][1].url}
                    className="w-full h-full"
                    width={300}
                    height={240}
                    loading="lazy"
                    quality={85}
                  />
                </div>
                <div className="w-full flex flex-col relative md:w-[65%]">
                  <p className="text-[#b7b7b7] text-[0.8rem] m-0 mb-7">{formattedDate(item.date_gmt)}</p>
                  <Heading content={item?.title?.rendered} />
                  <p className="m-0 text-[#747474] leading-[1.2rem] text-[0.9rem] pb-10 md:leading-6">{item.yoast_head_json.og_description}</p>
                  <p className="text-[#0169dd] text-[0.9rem] absolute m-0 bottom-0 right-0">
                    {t("read")} <FontAwesomeIcon icon={faSquareUpRight} />
                  </p>
                </div>
              </div>
            </Link>
          ))}
        </div>
        <div className="flex justify-center items-center my-8">
          {isLoading ? (
            <Loader />
          ) : (
            hasMore && (
              <button className="py-3 px-6 my-0 mx-auto rounded-lg border border-black text-[1.2rem] cursor-pointer bg-transparent" onClick={loadMoreArticles}>
                {t("loadMoreBtn")}
              </button>
            )
          )}
        </div>
      </section>
    </div>
  );
};

export default ArticlesClient;
