import { useTranslations } from "next-intl";
// import styles from "./styles.module.css";

export default function ErrorPopup({ isVisible, onClose, erroMessage, redirectHanlder }) {
  const t = useTranslations("errorPopup");

  if (!isVisible) return null;

  return (
    <div className="fixed top-0 left-0 w-full h-full bg-black bg-opacity-50 flex justify-center items-center z-[99]">
      <div className="card-base text-center w-[300px] shadow-lg">
        <h2 className="modal-title text-gray-800 mb-[10px]">{t("login_failed")}</h2>

        <p className="text-body-lg text-gray-600 mb-5">{erroMessage ?? t("incorrect_credentials")}</p>

        <button className="btn-primary w-[60%] mb-[10px]" onClick={onClose}>
          {t("ok")}
        </button>

        <a onClick={redirectHanlder} className="block text-[#6a5acd] mt-[10px] underline text-caption cursor-pointer">
          {t("forgot_password")}
        </a>
      </div>
    </div>
  );
}
