import { useTranslations } from "next-intl";
// import styles from "./styles.module.css";

export default function ErrorPopup({ isVisible, onClose, erroMessage, redirectHanlder }) {
  const t = useTranslations("errorPopup");

  if (!isVisible) return null;

  return (
    <div className="fixed top-0 left-0 w-full h-full bg-black bg-opacity-50 flex justify-center items-center z-[99]">
      <div className="bg-white p-5 rounded-[10px] text-center w-[300px] shadow-lg">
        <h2 className="text-xl text-gray-800 mb-[10px] font-nevermind-bold">{t("login_failed")}</h2>

        <p className="text-[1.1rem] text-gray-600 mb-5 font-nevermind-light">{erroMessage ?? t("incorrect_credentials")}</p>

        <button className="w-[60%] bg-[#6a5acd] text-white py-[10px] px-5 border-none rounded-[5px] cursor-pointer mb-[10px]" onClick={onClose}>
          {t("ok")}
        </button>

        <a onClick={redirectHanlder} className="block text-[#6a5acd] mt-[10px] underline text-[0.7rem] cursor-pointer">
          {t("forgot_password")}
        </a>
      </div>
    </div>
  );
}
