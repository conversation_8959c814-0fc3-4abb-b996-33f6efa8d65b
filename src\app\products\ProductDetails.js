"use client";
import { gamesData, themeFilters } from "@/constants";
import useScrollAnimation from "@/hooks/useScrollAnimation";
import dynamic from "next/dynamic";
import Image from "next/image";
import { useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";
import Carousel from "../../components/CarouselProduct";
const ModalFullScreen = dynamic(() => import("@/components/ModalFullScreen"));

const ProductsDetails = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedFilter, setSelectedFilter] = useState("All");
  const [modalData, setModalData] = useState([]);
  const searchParams = useSearchParams();

  const toggleModal = (gameData) => {
    setIsModalOpen((prev) => !prev);
    setModalData(gameData);
  };

  const handleFilterClick = (filter) => {
    setSelectedFilter(filter);
  };

  const filteredGames =
    selectedFilter === "All"
      ? gamesData
      : gamesData.filter((game) => game.interests.includes(selectedFilter.toUpperCase()));

  useEffect(() => {
    if (searchParams.get("theme")) {
      const section = document.getElementById("target-section");
      handleFilterClick(searchParams.get("theme"));
      section.scrollIntoView({ behavior: "smooth" });
    }
  }, []);

  const setRef = useScrollAnimation(
    {
      duration: 1,
      ease: "power3.out",
      start: "top bottom",
    },
    []
  );

  return (
    <>
      <main className="max-w-container mx-auto">
        <Carousel />
        <div className="w-[85%] mx-auto lg:w-[95%]">
          <div
            id="target-section"
            className="flex gap-4 overflow-x-auto scrollbar-none"
            // ref={(el) => setRef(el)}
          >
            {themeFilters.map((filter, index) => (
              <button
                key={index}
                onClick={() => handleFilterClick(filter)}
                className={`btn-filter ${
                  selectedFilter === filter ? "btn-filter-active" : ""
                }`}
              >
                {filter}
              </button>
            ))}
          </div>
          <section className="grid justify-items-center grid-cols-[repeat(auto-fit,minmax(300px,1fr))] gap-8 font-poppins mb-24 md:grid-cols-1">
            {filteredGames.map((game, index) => {
              return (
                <div
                  key={game.gameName}
                  onClick={() => toggleModal(game)}
                  className="card-product"
                  // ref={(el) => setRef(el)}
                >
                  <Image
                    src={game.gameThumbnailImg}
                    alt={game.gameName}
                    className="w-full h-[200px] object-cover rounded-xl"
                  />
                  <p className="heading-quaternary m-0">{game.gameName}</p>
                  <p className="text-body-sm text-black/50 m-0">{game.gameDesc}</p>
                  <div className="flex gap-[5px]">
                    <span className="skill-tag">{game.gameSkill}</span>
                  </div>
                  <button className="btn-secondary w-full my-4">Learn More</button>
                </div>
              );
            })}
          </section>
        </div>
        {isModalOpen && (
          <ModalFullScreen toggleModal={toggleModal} modalData={modalData} isOpen={isModalOpen} />
        )}
      </main>
    </>
  );
};

export default ProductsDetails;
