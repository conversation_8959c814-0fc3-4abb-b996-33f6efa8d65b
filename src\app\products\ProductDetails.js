"use client";
import { gamesData, themeFilters } from "@/constants";
import useScrollAnimation from "@/hooks/useScrollAnimation";
import dynamic from "next/dynamic";
import Image from "next/image";
import { useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";
import Carousel from "../../components/CarouselProduct";
const ModalFullScreen = dynamic(() => import("@/components/ModalFullScreen"));

const ProductsDetails = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedFilter, setSelectedFilter] = useState("All");
  const [modalData, setModalData] = useState([]);
  const searchParams = useSearchParams();

  const toggleModal = (gameData) => {
    setIsModalOpen((prev) => !prev);
    setModalData(gameData);
  };

  const handleFilterClick = (filter) => {
    setSelectedFilter(filter);
  };

  const filteredGames =
    selectedFilter === "All"
      ? gamesData
      : gamesData.filter((game) => game.interests.includes(selectedFilter.toUpperCase()));

  useEffect(() => {
    if (searchParams.get("theme")) {
      const section = document.getElementById("target-section");
      handleFilterClick(searchParams.get("theme"));
      section.scrollIntoView({ behavior: "smooth" });
    }
  }, []);

  const setRef = useScrollAnimation(
    {
      duration: 1,
      ease: "power3.out",
      start: "top bottom",
    },
    []
  );

  return (
    <>
      <main className="max-w-container mx-auto">
        <Carousel />
        <div className="w-[85%] mx-auto lg:w-[95%]">
          <div
            id="target-section"
            className="flex gap-4 overflow-x-auto scrollbar-none"
            // ref={(el) => setRef(el)}
          >
            {themeFilters.map((filter, index) => (
              <p
                key={index}
                onClick={() => handleFilterClick(filter)}
                className={`rounded-[40px] border border-[#d2d2d2] text-[#737373] font-poppins py-2 px-4 whitespace-nowrap cursor-pointer hover:bg-[#fc8800] hover:text-white hover:border-transparent ${
                  selectedFilter === filter ? "bg-[#fc8800] text-white border-transparent" : ""
                }`}
              >
                {filter}
              </p>
            ))}
          </div>
          <section className="grid justify-items-center grid-cols-[repeat(auto-fit,minmax(300px,1fr))] gap-8 font-poppins mb-24 md:grid-cols-1">
            {filteredGames.map((game, index) => {
              return (
                <div
                  key={game.gameName}
                  onClick={() => toggleModal(game)}
                  className="p-4 rounded-2xl border border-[#d9d9d9] bg-white max-w-[350px] shadow-card cursor-pointer flex flex-col justify-between hover:shadow-none hover:scale-[0.98]"
                  // ref={(el) => setRef(el)}
                >
                  <Image
                    src={game.gameThumbnailImg}
                    alt={game.gameName}
                    className="w-full h-[200px] object-cover rounded-xl"
                  />
                  <p className="font-bold text-2xl m-0">{game.gameName}</p>
                  <p className="font-normal text-[1.1rem] text-black/50 m-0">{game.gameDesc}</p>
                  <div className="flex gap-[5px]">
                    <p className="text-[0.9rem] bg-[#f7f7f7] py-2 px-4 rounded-[2.5rem] border border-[#e9e9e9]">{game.gameSkill}</p>
                  </div>
                  <button className="w-full my-4 bg-transparent text-[1.2rem] text-black border border-[#b0b0b0] rounded-2 py-3 px-8 cursor-pointer hover:text-white hover:bg-black hover:border-black">Learn More</button>
                </div>
              );
            })}
          </section>
        </div>
        {isModalOpen && (
          <ModalFullScreen toggleModal={toggleModal} modalData={modalData} isOpen={isModalOpen} />
        )}
      </main>
    </>
  );
};

export default ProductsDetails;
