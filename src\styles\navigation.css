/* Navigation Components */

/* Navbar Components */
.navbar {
  @apply flex justify-between items-center p-3 bg-white border-b border-[#dbdbdb]
         sticky top-0 z-[100] font-poppins font-medium w-full box-border;
}

.nav-link {
  @apply text-[#666666] no-underline py-2 px-2 block relative whitespace-nowrap
         transition-colors duration-300 ease-in-out
         after:content-[''] after:absolute after:bottom-0 after:left-1/2 after:transform
         after:-translate-x-1/2 after:w-0 after:h-1 after:bg-primary-400 after:rounded-sm
         after:transition-all after:duration-300 after:ease-in-out
         hover:text-black hover:after:w-full;
}

.nav-link.active {
  @apply text-black after:w-full;
}

.nav-menu {
  @apply flex list-none m-0 p-0 items-center flex-nowrap;
}

.nav-item {
  @apply ml-4 whitespace-nowrap;
}

.nav-hamburger {
  @apply hidden cursor-pointer;
}

.nav-hamburger-bar {
  @apply w-[30px] h-[3px] bg-black my-1 transition-all duration-[0.4s];
}

.nav-hamburger.active .nav-hamburger-bar:nth-child(1) {
  @apply rotate-[-45deg] translate-x-[-5px] translate-y-[6px];
}

.nav-hamburger.active .nav-hamburger-bar:nth-child(2) {
  @apply opacity-0;
}

.nav-hamburger.active .nav-hamburger-bar:nth-child(3) {
  @apply rotate-[45deg] translate-x-[-5px] translate-y-[-6px];
}

.nav-free-trial-btn {
  @apply font-semibold text-2xl bg-primary-400 text-white rounded-lg shadow-button
         py-2 px-6 whitespace-nowrap transition-colors duration-300 hover:text-white;
}

/* Mobile Navbar Styles */
@media (max-width: 820px) {
  .navbar {
    @apply px-3 py-3 max-w-full overflow-x-hidden;
  }

  .nav-hamburger {
    @apply block;
  }

  .nav-menu {
    @apply flex-col w-full h-screen fixed top-0 bg-white items-center
           transition-all duration-300 overflow-y-auto overflow-x-hidden p-4 box-border;
  }

  .nav-menu.closed {
    @apply right-[-150%];
  }

  .nav-menu.open {
    @apply right-0 z-20;
  }

  .nav-item {
    @apply my-4 text-2xl text-center ml-0;
  }

  .nav-link {
    @apply text-xl p-2;
  }

  .nav-free-trial-btn {
    @apply text-xl inline-block text-center;
  }
}
