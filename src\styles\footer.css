/* Footer Components */

/* Footer Components */
.footer-wrapper {
  @apply max-w-[1728px] mx-auto;
}

.pink-footer-wrapper {
  @apply bg-[url('/images/pinkBg.webp')] bg-cover bg-no-repeat bg-center w-full pt-[8.3rem] pb-6;
}

.lock-content-wrapper {
  @apply flex items-center flex-col text-[2.5rem] md:text-[5rem];
}

.lock-content-wrapper p:first-child {
  @apply m-0;
}

.lock-content-wrapper p:nth-child(2) {
  @apply text-white my-4 mx-0 md:mb-8;
}

.footer-cta-button {
  @apply mt-0 bg-primary-400 text-2xl text-white rounded-2xl py-3 px-8 no-underline border-none
         shadow-button cursor-pointer font-poppins font-medium
         md:text-[2rem] md:py-3 md:px-12;
}

.game-cards-wrapper {
  @apply flex gap-[10px] justify-center text-center mt-8 mx-8 mb-0 md:gap-5;
}

.game-cards-section {
  @apply flex gap-[10px] flex-wrap;
}

.game-cards-section:first-child {
  @apply justify-end gap-5;
}

.game-cards-section:last-child {
  @apply justify-start gap-5;
}

.game-card-col1 div {
  @apply bg-white p-[5px] rounded-xl md:p-[3px];
}

.game-card-col1 p {
  @apply m-0 text-xs md:text-base;
}

.game-card-col2 {
  @apply flex flex-col justify-between gap-[15px];
}

.game-card-col2 div {
  @apply bg-white p-[5px] rounded-xl md:p-[3px];
}

.game-card-col2 p {
  @apply m-0 text-xs md:text-base;
}

.footer-green-wrapper {
  @apply relative bg-[url('/images/footer.webp')] bg-cover bg-no-repeat bg-top w-full
         min-h-[1250px] flex items-end
         md:min-h-[1050px]
         lg:min-h-[830px]
         xl:min-h-[1200px];
}

.footer-content-wrapper {
  @apply flex justify-center items-center absolute w-[98%] left-1/2
         transform -translate-x-1/2 text-white rounded-xl flex-col bottom-[10px]
         md:flex-row;
  background-color: rgba(255, 255, 255, 0.12);
}

.footer-content-wrapper > div {
  @apply w-full;
}

.footer-content-left {
  @apply flex flex-col gap-[13px] p-0 md:gap-[90px] md:pl-8;
}

.footer-content-left > div {
  @apply w-4/5 ml-2;
}

.footer-content-left-img {
  @apply flex gap-[5px];
}

.footer-content-right {
  @apply flex flex-col border-l-0 md:border-l;
  border-color: rgba(255, 255, 255, 0.3);
}

.footer-content-right-top {
  @apply flex w-full text-left border-b border-t mt-8 flex-col p-4 box-border
         md:border-t-0 md:mt-0 md:flex-row md:p-0;
  border-color: rgba(255, 255, 255, 0.3);
}

.footer-content-right-top > div {
  @apply w-full ml-[0.3rem] md:w-1/3 md:ml-4;
}

.footer-content-right-top > div:nth-child(1),
.footer-content-right-top > div:nth-child(2) {
  @apply md:border-r;
  border-color: rgba(255, 255, 255, 0.3);
}

.footer-content-right-top ul {
  @apply list-none text-left p-0 m-0 text-xl font-poppins;
}

.footer-content-right-top li {
  @apply my-2;
  color: rgba(255, 255, 255, 0.7);
}

.footer-link-hoverable:hover {
  @apply inline my-2 text-white border-b border-white box-border;
}

.store-icons-wrapper {
  @apply grid gap-4 grid-cols-4 absolute bottom-[280px] left-1/2 transform -translate-x-1/2
         max-[700px]:grid-cols-2 max-[700px]:bottom-[680px];
}

.store-icons {
  @apply cursor-pointer hover:scale-105 transition-transform duration-300
         max-[700px]:w-[178px] max-[700px]:h-[50px]
         md:max-lg:w-[140px] md:max-lg:h-[50px];
}

.footer-content-wrapper > div:first-child {
  @apply md:w-2/5;
}
