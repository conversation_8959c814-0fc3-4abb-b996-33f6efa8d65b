"use client";

import EmailOtpInput from "@/components/EmailOtpInput";
import FormCtaButton from "@/components/FormCtaButton";
import Stepper from "@/components/Stepper";
import { useAuth } from "@/context/AuthContext";
import useTogglePinkFooter from "@/hooks/useTogglePinkFooter";
import apiClient from "@/utils/axiosUtil";
import { getLocale } from "@/utils/helperFunctions";
import { webEngagelogin } from "@/utils/webengage";
import { faCheck } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { useLocale, useTranslations } from "next-intl";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useCallback, useEffect, useState } from "react";

const OTPLoginPage = () => {
  const router = useRouter();
  const [state, setState] = useState({
    step: 1,
    email: "",
    otp: "",
    loading: false,
    error: "",
    serverError: false,
  });
  const [resendState, setResendState] = useState({
    timer: 30,
    status: "waiting", // "waiting", "active", "resent"
  });
  const { login } = useAuth();
  const locale = useLocale();
  const t = useTranslations("LoginOtp");
  const lang = getLocale(locale);

  useEffect(() => {
    let interval;
    if (resendState.timer > 0 && resendState.status === "waiting") {
      interval = setInterval(() => {
        setResendState((prev) => ({ ...prev, timer: prev.timer - 1 }));
      }, 1000);
    } else if (resendState.timer === 0 && resendState.status === "waiting") {
      setResendState((prev) => ({ ...prev, status: "active" }));
    }
    return () => clearInterval(interval);
  }, [resendState.timer, resendState.status]);

  const validateEmail = useCallback(
    (email) => {
      if (!email) return t("EmailIncorrect");
      if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) return t("EmailIncorrect");
      return "";
    },
    [t]
  );

  const validateOtp = useCallback(
    (otp) => {
      if (otp.length !== 4 || otp.split("").some((char) => isNaN(char) || char === "")) {
        return t("OtpError");
      }
      return "";
    },
    [t]
  );

  const handleApiRequest = useCallback(
    async (url, body, successMessage, isLoadingEnabled = true) => {
      try {
        setState((prev) => ({
          ...prev,
          loading: isLoadingEnabled,
          error: "",
          serverError: false,
        }));

        const response = await apiClient.post(`${url}`, body);

        if (response) {
          return response.data;
        } else {
          setState((prev) => ({
            ...prev,
            loading: false,
            error: response?.data?.Message ?? t("OperationFailed"),
            serverError: true,
          }));
          return null;
        }
      } catch (error) {
        setState((prev) => ({
          ...prev,
          loading: false,
          error: error.response?.data?.Message || t("ErrorOccurred"),
          serverError: true,
        }));
        return null;
      }
    },
    [t]
  );

  const handleGenerateOtp = useCallback(
    async (e) => {
      e.preventDefault();
      const emailError = validateEmail(state.email);

      if (emailError) {
        setState((prev) => ({ ...prev, error: emailError }));
        return;
      }

      const data = await handleApiRequest(
        `${process.env.NEXT_PUBLIC_USERSERVICE_BASE_URL}/login/generate_otp?l=${lang}&gameid=hungrycat&version=8.0&platform=iOS`,
        { gameid: "hungrycat", email: state.email },
        state.otp ? t("OtpRegenerated") : t("OtpSent")
      );

      if (data) {
        setState((prev) => ({ ...prev, step: 2, loading: false }));
        setResendState({ timer: 30, status: "waiting" });
      }
    },
    [state.email, state.otp, handleApiRequest, validateEmail, lang, t]
  );

  const handleVerifyOtp = useCallback(
    async (e) => {
      e.preventDefault();
      const otpError = validateOtp(state.otp);

      if (otpError) {
        setState((prev) => ({ ...prev, error: otpError }));
        return;
      }

      const data = await handleApiRequest(
        `${process.env.NEXT_PUBLIC_USERSERVICE_BASE_URL}/login/verify_otp?l=${lang}&gameid=hungrycat&version=8.0&platform=iOS`,
        { gameid: "hungrycat", email: state.email, otp: state.otp },
        t("OtpVerified")
      );

      if (data) {
        const auth_token = localStorage.getItem("auth_token");
        document.cookie = `token=${auth_token}; Path=/; Secure; SameSite=Strict; Max-Age=${process.env.NEXT_PUBLIC_COOKIE_EXPIRY}`;
        localStorage.setItem(
          "UserDetails",
          JSON.stringify({
            email: data.Email,
            expiryDate: data.SubscriptionExpire,
            isSubscribed: data.isSubscribed,
            ...data,
          })
        );
        login(data.isSubscribed);
        webEngagelogin({ email: data.Email, userId: data.Id });
        if (data.isSubscribed) {
          router.push("/user-home-screen");
        } else {
          router.push("/profile");
        }
      }
    },
    [state.email, state.otp, validateOtp, router, handleApiRequest, login, lang, t]
  );

  const handleResendOtp = useCallback(async () => {
    if (resendState.status !== "active") return;

    const data = await handleApiRequest(
      `${process.env.NEXT_PUBLIC_USERSERVICE_BASE_URL}/login/generate_otp?l=${lang}&gameid=hungrycat&version=8.0&platform=iOS`,
      { gameid: "hungrycat", email: state.email },
      t("OtpResent"),
      false
    );

    if (data) {
      setResendState({ timer: 30, status: "resent" });
      setTimeout(() => setResendState({ timer: 30, status: "waiting" }), 3000); // Reset after 3 seconds
    }
  }, [resendState.status, state.email, handleApiRequest, lang, t]);

  const handleBack = useCallback(() => {
    if (state.step === 2) {
      setState((prev) => ({ ...prev, step: 1, error: "" }));
    } else {
      router.back();
    }
  }, [state.step, router]);

  useTogglePinkFooter(false);

  return (
    <div className="bg-[#f9f9f9] flex items-center justify-center py-0 pr-0 pb-[2vh] pl-0">
      <div className="w-full max-w-[480px] mx-auto py-0 px-[2vw] pb-[6vh] flex flex-col items-center font-nevermind-medium bg-white shadow-[0_0.5vmin_2vmin_rgba(0,0,0,0.1)] max-[480px]:rounded-none max-[480px]:h-full">
        <div style={{ width: "100%" }}>
          <Stepper
            currentStep={state.step === 1 ? 3 : 4}
            handleBack={handleBack}
            steps={[1, 2, 3, 4]}
          />
        </div>
        <h1 className="font-nevermind-bold text-[clamp(1.5rem,4vw,2rem)] font-bold mb-0 text-left w-full">{state.step === 1 ? t("Heading") : t("HeadingOtp")}</h1>
        {state.step === 1 ? (
          <p className="w-full text-[1rem] font-nevermind-light">{t("Subheading")}</p>
        ) : (
          <p className="w-full text-[1rem] font-nevermind-light">
            {t("SubheadingOtp")} {state.email}
          </p>
        )}
        <form
          className="w-full flex flex-col gap-[3vh] relative z-[1]"
          onSubmit={state.step === 1 ? handleGenerateOtp : handleVerifyOtp}
        >
          <div className="relative w-full flex flex-col gap-[3vh]">
            {state.step === 1 ? (
              <div className="relative w-full">
                <input
                  type="email"
                  placeholder={t("InputPlaceholder")}
                  className={`w-full py-[2vh] px-[3vw] border-[0.2vmin] border-solid border-[#e0e0e0] rounded-[2vmin] text-[clamp(0.875rem,2vw,1rem)] transition-[border-color] duration-200 box-border placeholder:text-[#6c757d] ${state.error ? "border-[#dd4a38] text-[#dd4a38]" : ""}`}
                  value={state.email}
                  onChange={(e) =>
                    setState((prev) => ({ ...prev, email: e.target.value, error: "" }))
                  }
                />
              </div>
            ) : (
              <EmailOtpInput
                error={state.error}
                value={state.otp}
                onOtpChange={(value) => setState((prev) => ({ ...prev, otp: value, error: "" }))}
              />
            )}

            {state.error && (
              <div className="bg-[#f8d7ce] text-[#dd4a38] my-[1vh] mx-0 rounded-[1vmin] py-[1vh] px-[2vw] text-[clamp(0.75rem,1.5vw,0.875rem)] flex items-center">
                <Image src="/images/webGl/warning.png" height={20} width={20} alt="Warning" className="mr-[1vw] w-[clamp(16px,3vw,20px)] h-auto" />
                {state.error}
              </div>
            )}

            {state.step === 2 && (
              <div className="text-center mb-[2vh]">
                {resendState.status === "waiting" && (
                  <button className="bg-[#f1f1f1] text-black border-none py-[1vh] px-[2vw] text-[clamp(0.875rem,2vw,1rem)] rounded-[1vmin] cursor-not-allowed bg-[#ddd] text-[#999]" disabled>
                    {t("ResendTimer", { timer: resendState.timer })}
                  </button>
                )}
                {resendState.status === "active" && (
                  <button type="button" className="bg-[#f1f1f1] text-black border-none py-[1vh] px-[2vw] text-[clamp(0.875rem,2vw,1rem)] rounded-[1vmin] cursor-pointer transition-[background-color,color] duration-200" onClick={handleResendOtp}>
                    {t("Resend")}
                  </button>
                )}
                {resendState.status === "resent" && (
                  <button className="bg-[#4caf50] text-white border-none py-[1vh] px-[2vw] text-[clamp(0.875rem,2vw,1rem)] rounded-[1vmin] cursor-pointer">
                    {t("Resent")} <FontAwesomeIcon icon={faCheck} className="ml-2" />
                  </button>
                )}
              </div>
            )}
          </div>
          <Image
            src="/images/webGl/EmailId/activeGraphic.png"
            className="absolute left-1/2 bottom-0 transform -translate-x-1/2 z-[-1] pointer-events-none w-[72%] h-auto max-h-[85%] object-contain object-bottom"
            width={300}
            height={400}
            alt="BackgroundImg"
          />

          {/* 24vh */}
          <FormCtaButton
            text={state.step === 1 ? t("CtaBtnGetCode") : t("CtaLogin")}
            loading={state.loading}
            customStyles={
              state.step === 1
                ? state.error
                  ? "margin-top: 26vh !important;"
                  : "margin-top: 36vh !important;"
                : state.error
                  ? "margin-top: 6vh !important;"
                  : "margin-top: 18vh !important;"
            }
          />
        </form>
      </div>
    </div>
  );
};

export default OTPLoginPage;
