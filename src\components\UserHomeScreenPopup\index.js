import { useEffect } from "react";

import { extractGameData } from "@/utils/helperFunctions";
import { trackWebEngageEvent } from "@/utils/webengage";
import Image from "next/image";
import { useRouter } from "next/navigation";
// import styles from "./styles.module.css";

const UserHomeScreenPopup = ({ isOpen, onClose, isDark, cardData }) => {
  useEffect(() => {
    if (isOpen) {
      document.body.classList.add("overflow-hidden");
    } else {
      document.body.classList.remove("overflow-hidden");
    }
    return () => {
      document.body.classList.remove("overflow-hidden");
    };
  }, [isOpen]);
  if (!isOpen) return null;

  const popupBottomImg = isDark
    ? "/images/webGl/userHomeScreen/crater.webp"
    : "/images/webGl/userHomeScreen/grass.webp";
  const router = useRouter();

  return (
    <div className="fixed top-0 left-0 w-full h-full bg-black bg-opacity-50 flex justify-center items-center z-[1000]">
      <div className="bg-white rounded-[10px] w-full rounded-tl-[100px] rounded-tr-[100px] relative absolute bottom-0 box-border max-[500px]:landscape:rounded-tl-[70px] max-[500px]:landscape:rounded-tr-[70px]">
        <div className="flex items-center justify-between py-8 px-16 box-border max-[500px]:landscape:py-4 max-[500px]:landscape:px-12">
          <div onClick={onClose}>
            <Image
              src="/images/webGl/userHomeScreen/bckBtn.webp"
              height={50}
              width={50}
              className="cursor-pointer max-[500px]:landscape:w-[40px] max-[500px]:landscape:h-[40px]"
              alt="Back button"
            />
          </div>
          <p className="text-[2.5rem] mb-4 mt-0 text-center text-[#007d88] max-[500px]:landscape:text-[2rem]">{cardData.TileName}</p>
          <div></div>
        </div>
        <div className="mx-16 my-0">
          <div className="text-center flex gap-10 justify-around py-0 px-16 pb-28 overflow-x-auto overflow-y-hidden scrollbar-none relative z-[1002] max-[500px]:landscape:gap-5 max-[500px]:landscape:py-0 max-[500px]:landscape:px-12 max-[500px]:landscape:pb-14">
            {cardData.GameCollection &&
              cardData.GameCollection.map((item, index) => {
                const { gameUrl, gameOrientation } = extractGameData(item.WebGameUrl);
                return (
                  <div
                    key={index}
                    role="button"
                    tabIndex={0}
                    aria-label={`Play ${item.DisplayName}`}
                    onClick={() => {
                      trackWebEngageEvent("WebGlGameClk", {
                        current_game: item?.GameID,
                      });
                      router.push(
                        `/game-player?gameUrl=${gameUrl}&gameName=Track-Rotate&gameOrientation=${gameOrientation}`
                      );
                    }}
                    style={{ cursor: "pointer" }}
                    onKeyDown={() => {}}
                    className="p-4 bg-white rounded-[32px] relative shadow-[0px_9.01px_0px_0px_#000000] border border-gray-300 noLinkStyle max-[500px]:landscape:p-3 max-[500px]:landscape:rounded-[22px]"
                  >
                    <Image
                      src={item.IconURL}
                      width={220}
                      height={220}
                      className="max-w-none max-[500px]:landscape:w-[150px] max-[500px]:landscape:h-[150px]"
                      alt={item.DisplayName}
                    />
                    <p className="font-poppins font-medium text-[1.3rem] my-2 mx-0 max-[500px]:landscape:text-base max-[500px]:landscape:my-1 max-[500px]:landscape:mx-0">{item.DisplayName}</p>
                    <Image
                      width={65}
                      height={65}
                      src="/images/webGl/userHomeScreen/playBtnPopup.png"
                      className="absolute -bottom-[35px] left-20 max-[500px]:landscape:-bottom-[43px] max-[500px]:landscape:left-[70px]"
                      alt="Play Button"
                    />
                  </div>
                );
              })}
          </div>
        </div>

        <div className="p-0 absolute -bottom-[10px] -left-[30px] scale-x-[-1] z-[1001]">
          <Image src={popupBottomImg} width={180} height={80} className="max-[500px]:landscape:w-[150px] max-[500px]:landscape:h-[50px]" />
        </div>
        <div className="p-0 absolute -bottom-[10px] -right-[30px] z-[1001]">
          <Image src={popupBottomImg} width={180} height={80} className="max-[500px]:landscape:w-[150px] max-[500px]:landscape:h-[50px]" />
        </div>
      </div>
    </div>
  );
};

export default UserHomeScreenPopup;
