"use client";
import Image from "next/image";
import { useEffect } from "react";
// import styles from "./styles.module.css";
import Link from "next/link";

const ModalFullScreen = ({ toggleModal, modalData, isOpen }) => {
  useEffect(() => {
    if (isOpen) {
      document.body.classList.add("overflow-hidden");
    } else {
      document.body.classList.remove("overflow-hidden");
    }
    return () => {
      document.body.classList.remove("overflow-hidden");
    };
  }, [isOpen]);
  return (
    <>
      <button className="p-2 text-base cursor-pointer" onClick={toggleModal}>
        Open Modal
      </button>
      <div className="fixed top-0 left-0 w-full h-full bg-black bg-opacity-70 flex justify-center items-center z-[1000]" onClick={toggleModal}>
        <div className="max-w-container mx-auto bg-white rounded-lg p-8 w-[90%] max-h-[80%] overflow-y-auto relative font-poppins md:w-[90%] md:rounded-none md:px-2" onClick={(e) => e.stopPropagation()}>
          <button className="absolute top-0 right-0 bg-none border-none text-2xl cursor-pointer z-[1001] md:top-1 md:right-1" onClick={toggleModal}>
            X
          </button>
          <div className="flex gap-5 overflow-x-auto scrollbar-none">
            {modalData.gamePopinImages.map((img, index) => (
              <div className="object-fill" key={index}>
                <Image
                  src={img}
                  width={184}
                  height={328}
                  alt={`Image ${index + 1}`}
                  placeholder="blur"
                  blurDataURL="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNctmhZPQAGUwJv7ObBEQAAAABJRU5ErkJggg=="
                  className="rounded-lg"
                />
              </div>
            ))}
          </div>
          <div>
            <h2 className="text-[2.3rem] mt-6 mb-0">{modalData.gameName}</h2>
            <p className="font-normal text-2xl text-black text-opacity-50 m-0">{modalData.gameDesc}</p>
            <p>{modalData.gameContentInside.description1}</p>
            <p>{modalData.gameContentInside.description2}</p>
            <div className="flex justify-between gap-5">
              {modalData.storeLinks?.appStore && (
                <Link
                  className="text-[1.2rem] w-[48%] rounded-[9px] py-4 px-2 text-white bg-[#1f5dfe] no-underline border-none cursor-pointer text-center"
                  href={modalData.storeLinks.appStore}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  App store
                </Link>
              )}
              {modalData.storeLinks?.playStore && (
                <Link
                  className="text-[1.2rem] w-[48%] rounded-[9px] py-4 px-2 text-white bg-[#00800d] no-underline border-none cursor-pointer text-center"
                  href={modalData.storeLinks.playStore}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  Play store
                </Link>
              )}
            </div>
          </div>
        </div>
      </div>
    </>
  );
};
export default ModalFullScreen;
