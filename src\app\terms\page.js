import { useTranslations } from "next-intl";

export const metadata = {
  title: "SKIDOS Terms | Terms of Service",
};

const TeamsPage = () => {
  const t = useTranslations("terms");

  return (
    <>
      <div className="font-poppins">
        <div className="text-center leading-[1] font-nevermind-bold font-bold">
          <h1>{t("pageTitle")}</h1>
        </div>
        <div className="px-8 py-0">
          <p className="text-black text-[17px] font-normal leading-[1.4em] mb-[7px] pb-[27px] text-justify font-poppins">{t("lastUpdated")}</p>
          <p className="text-black text-[17px] font-normal leading-[1.4em] mb-[7px] pb-[27px] text-justify font-poppins">{t("ageWarning")}</p>
          <p className="text-black text-[17px] font-normal leading-[1.4em] mb-[7px] pb-[27px] text-justify font-poppins">{t("accessAcknowledge")}</p>
          <p className="text-black text-[17px] font-normal leading-[1.4em] mb-[7px] pb-[27px] text-justify font-poppins">{t("contact")}</p>
          <p className="text-black text-[17px] font-normal leading-[1.4em] mb-[7px] pb-[27px] text-justify font-poppins">
            {t("exitWarning")}
            <a href="https://skidos.com/"> www.skidos.com</a>&nbsp;
          </p>

          <p className="text-black text-[17px] font-normal leading-[1.4em] mb-[7px] pb-[27px] text-justify font-poppins">
            <strong>{t("sections.acknowledgment.title")}</strong>
          </p>
          <p className="text-black text-[17px] font-normal leading-[1.4em] mb-[7px] pb-[27px] text-justify font-poppins">{t("sections.acknowledgment.content")}</p>

          <p className="text-black text-[17px] font-normal leading-[1.4em] mb-[7px] pb-[27px] text-justify font-poppins">
            <strong>{t("sections.userInfo.title")}</strong>
          </p>
          <p className="text-black text-[17px] font-normal leading-[1.4em] mb-[7px] pb-[27px] text-justify font-poppins">{t("sections.userInfo.content")}</p>

          <p className="text-black text-[17px] font-normal leading-[1.4em] mb-[7px] pb-[27px] text-justify font-poppins">
            <strong>{t("sections.userAccount.title")}</strong>
          </p>
          <p className="text-black text-[17px] font-normal leading-[1.4em] mb-[7px] pb-[27px] text-justify font-poppins">{t("sections.userAccount.content")}</p>

          <p className="text-black text-[17px] font-normal leading-[1.4em] mb-[7px] pb-[27px] text-justify font-poppins">
            <strong>{t("sections.conduct.title")}</strong>
          </p>
          <p className="text-black text-[17px] font-normal leading-[1.4em] mb-[7px] pb-[27px] text-justify font-poppins">{t("sections.conduct.content")}</p>

          <p className="text-black text-[17px] font-normal leading-[1.4em] mb-[7px] pb-[27px] text-justify font-poppins">
            <strong>{t("sections.intellectualProperty.title")}</strong>
          </p>
          <p className="text-black text-[17px] font-normal leading-[1.4em] mb-[7px] pb-[27px] text-justify font-poppins">{t("sections.intellectualProperty.content")}</p>

          <p className="text-black text-[17px] font-normal leading-[1.4em] mb-[7px] pb-[27px] text-justify font-poppins">
            <strong>{t("sections.charges.title")}</strong>
          </p>
          <p className="text-black text-[17px] font-normal leading-[1.4em] mb-[7px] pb-[27px] text-justify font-poppins">{t("sections.charges.content")}</p>

          <p className="text-black text-[17px] font-normal leading-[1.4em] mb-[7px] pb-[27px] text-justify font-poppins">
            <strong>{t("refundPolicy.title")}</strong>
          </p>
          <p className="text-black text-[17px] font-normal leading-[1.4em] mb-[7px] pb-[27px] text-justify font-poppins">{t("refundPolicy.applicability")}</p>
          <ul className="text-black text-[17px] font-normal leading-[1.4em] mb-[7px] pb-[27px] text-justify font-poppins">
            <li>{t("refundPolicy.points.autoRenewal")}</li>
            <li>{t("refundPolicy.points.refundRequest")}</li>
            <li>{t("refundPolicy.points.discretion")}</li>
            <li>{t("refundPolicy.points.eligibility")}</li>
            <li>{t("refundPolicy.points.refundMethod")}</li>
          </ul>

          <p className="text-black text-[17px] font-normal leading-[1.4em] mb-[7px] pb-[27px] text-justify font-poppins">{t("effectiveDate")}</p>
        </div>
      </div>
    </>
  );
};

export default TeamsPage;
