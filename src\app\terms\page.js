import { useTranslations } from "next-intl";

export const metadata = {
  title: "SKIDOS Terms | Terms of Service",
};

const TeamsPage = () => {
  const t = useTranslations("terms");

  return (
    <>
      <div className="container-main section-padding">
        <div className="section-heading">
          <h1 className="heading-primary">{t("pageTitle")}</h1>
        </div>
        <div className="px-8 py-0">
          <p className="text-body text-justify mb-[7px] pb-[27px]">{t("lastUpdated")}</p>
          <p className="text-body text-justify mb-[7px] pb-[27px]">{t("ageWarning")}</p>
          <p className="text-body text-justify mb-[7px] pb-[27px]">{t("accessAcknowledge")}</p>
          <p className="text-body text-justify mb-[7px] pb-[27px]">{t("contact")}</p>
          <p className="text-body text-justify mb-[7px] pb-[27px]">
            {t("exitWarning")}
            <a href="https://skidos.com/" className="text-primary-400 hover:underline"> www.skidos.com</a>&nbsp;
          </p>

          <p className="text-body text-justify mb-[7px] pb-[27px]">
            <strong className="text-subheading">{t("sections.acknowledgment.title")}</strong>
          </p>
          <p className="text-body text-justify mb-[7px] pb-[27px]">{t("sections.acknowledgment.content")}</p>

          <p className="text-body text-justify mb-[7px] pb-[27px]">
            <strong className="text-subheading">{t("sections.userInfo.title")}</strong>
          </p>
          <p className="text-body text-justify mb-[7px] pb-[27px]">{t("sections.userInfo.content")}</p>

          <p className="text-body text-justify mb-[7px] pb-[27px]">
            <strong className="text-subheading">{t("sections.userAccount.title")}</strong>
          </p>
          <p className="text-body text-justify mb-[7px] pb-[27px]">{t("sections.userAccount.content")}</p>

          <p className="text-body text-justify mb-[7px] pb-[27px]">
            <strong className="text-subheading">{t("sections.conduct.title")}</strong>
          </p>
          <p className="text-body text-justify mb-[7px] pb-[27px]">{t("sections.conduct.content")}</p>

          <p className="text-body text-justify mb-[7px] pb-[27px]">
            <strong className="text-subheading">{t("sections.intellectualProperty.title")}</strong>
          </p>
          <p className="text-body text-justify mb-[7px] pb-[27px]">{t("sections.intellectualProperty.content")}</p>

          <p className="text-body text-justify mb-[7px] pb-[27px]">
            <strong className="text-subheading">{t("sections.charges.title")}</strong>
          </p>
          <p className="text-body text-justify mb-[7px] pb-[27px]">{t("sections.charges.content")}</p>

          <p className="text-body text-justify mb-[7px] pb-[27px]">
            <strong className="text-subheading">{t("refundPolicy.title")}</strong>
          </p>
          <p className="text-body text-justify mb-[7px] pb-[27px]">{t("refundPolicy.applicability")}</p>
          <ul className="text-body text-justify mb-[7px] pb-[27px] list-disc ml-6">
            <li className="mb-2">{t("refundPolicy.points.autoRenewal")}</li>
            <li className="mb-2">{t("refundPolicy.points.refundRequest")}</li>
            <li className="mb-2">{t("refundPolicy.points.discretion")}</li>
            <li className="mb-2">{t("refundPolicy.points.eligibility")}</li>
            <li className="mb-2">{t("refundPolicy.points.refundMethod")}</li>
          </ul>

          <p className="text-body text-justify mb-[7px] pb-[27px]">{t("effectiveDate")}</p>
        </div>
      </div>
    </>
  );
};

export default TeamsPage;
