import { useAuth } from "@/context/AuthContext";
import useIsMobile from "@/hooks/useIsMobile";
import { useTranslations } from "next-intl";
import Image from "next/image";
import { useRouter } from "next/navigation";
import "swiper/css";
import "swiper/css/pagination";
import { Autoplay, Pagination } from "swiper/modules";
import { Swiper, SwiperSlide } from "swiper/react";
import SideAvatar from "../../../public/images/product/Side_avatar.webp";
import SideIris from "../../../public/images/product/Side_IRIS.webp";
import SideMath from "../../../public/images/product/Side_math.webp";
import SideSel from "../../../public/images/product/Side_sel.webp";
import SideTracing from "../../../public/images/product/Side_tracing.webp";
import Loader from "../Loader";
import "./carousel.css";
// import styles from "./styles.module.css";

export default function App() {
  const t = useTranslations("Carousel1");
  const isMobile = useIsMobile();
  const router = useRouter();
  const { isSubscribed } = useAuth();

  const handleRedirect = () => {
    if (isSubscribed) {
      router.push("/user-home-screen");
    } else {
      router.push("/acquisition");
    }
  };

  if (isMobile === null) return <Loader />;
  return (
    <>
      <Swiper
        spaceBetween={1}
        pagination={{
          clickable: true,
          el: ".custom-pagination",
        }}
        autoplay={{
          delay: 5000,
          disableOnInteraction: false,
        }}
        modules={[Autoplay, Pagination]}
        className="mySwiper"
      >
        <SwiperSlide>
          {!isMobile ? (
            <div className="max-w-full bg-[url('/images/product/math.webp')] bg-[#e792cc] bg-cover bg-center bg-no-repeat flex object-cover">
              <div className="flex justify-center flex-col px-16 flex-wrap box-border w-1/2 text-white md:px-4">
                <h2 className="text-[5rem] m-0 md:text-[3rem]">{t("slides.math.title")}</h2>
                <p className="text-2xl font-poppins md:text-[1.1rem]">{t("slides.math.description")}</p>
                <button className="w-[70%] mt-8 bg-primary-400 text-[2.2rem] text-white rounded-2xl py-3 px-8 no-underline border-none shadow-button cursor-pointer mr-20 font-poppins font-medium md:mt-4 md:text-2xl md:rounded-[1.3rem]" onClick={handleRedirect}>
                  {t("common.getStarted")}
                </button>
              </div>
              <div className="w-1/2 text-white">
                <Image
                  alt={t("slides.math.imageAlt")}
                  src={SideMath}
                  className="w-full h-full"
                  priority
                />
              </div>
            </div>
          ) : (
            <div className="w-full h-[600px] bg-[url('/images/product/mobile_math.webp')] bg-cover bg-center bg-no-repeat flex object-contain">
              <div className="flex flex-col items-center text-white my-0 mx-auto">
                <h2 className="text-[2rem] mb-2">{t("slides.math.title")}</h2>
                <p className="my-2 mx-2 text-xl text-center font-poppins">{t("slides.math.description")}</p>
                <button className="font-poppins font-medium mt-6 bg-primary-400 text-2xl text-white rounded-2xl py-3 px-8 no-underline border-none shadow-[0px_10px_6px_rgba(0,0,0,0.978)] cursor-pointer" onClick={handleRedirect}>
                  {t("common.getStarted")}
                </button>
              </div>
            </div>
          )}
        </SwiperSlide>

        <SwiperSlide>
          {!isMobile ? (
            <div className="max-w-full bg-[url('/images/product/avatar.webp')] bg-[#01b0fa] bg-cover bg-center bg-no-repeat flex object-cover">
              <div className="flex justify-center flex-col px-16 flex-wrap box-border w-1/2 text-white md:px-4">
                <h2 className="text-[5rem] m-0 md:text-[3rem]">{t("slides.avatars.title")}</h2>
                <p className="text-2xl font-poppins md:text-[1.1rem]">{t("slides.avatars.description")}</p>
                <button className="w-[70%] mt-8 bg-primary-400 text-[2.2rem] text-white rounded-2xl py-3 px-8 no-underline border-none shadow-button cursor-pointer mr-20 font-poppins font-medium md:mt-4 md:text-2xl md:rounded-[1.3rem]" onClick={handleRedirect}>
                  {t("common.getStarted")}
                </button>
              </div>
              <div className="w-1/2 text-white">
                <Image
                  alt={t("slides.avatars.imageAlt")}
                  src={SideAvatar}
                  className="w-full h-full"
                  priority
                />
              </div>
            </div>
          ) : (
            <div className="w-full h-[600px] bg-[url('/images/product/mobile_avatar.webp')] bg-cover bg-center bg-no-repeat flex object-contain">
              <div className="flex flex-col items-center text-white my-0 mx-auto">
                <h2 className="text-[2rem] mb-2">{t("slides.avatars.title")}</h2>
                <p className="my-2 mx-2 text-xl text-center font-poppins">{t("slides.avatars.description")}</p>
                <button className="font-poppins font-medium mt-6 bg-primary-400 text-2xl text-white rounded-2xl py-3 px-8 no-underline border-none shadow-[0px_10px_6px_rgba(0,0,0,0.978)] cursor-pointer" onClick={handleRedirect}>
                  {t("common.getStarted")}
                </button>
              </div>
            </div>
          )}
        </SwiperSlide>

        <SwiperSlide>
          {!isMobile ? (
            <div className="max-w-full bg-[url('/images/product/iris.webp')] bg-[#02c7a5] bg-cover bg-center bg-no-repeat flex object-cover">
              <div className="flex justify-center flex-col px-16 flex-wrap box-border w-1/2 text-white md:px-4">
                <h2 className="text-[5rem] m-0 md:text-[3rem]">{t("slides.reading.title")}</h2>
                <p className="text-2xl font-poppins md:text-[1.1rem]">{t("slides.reading.description")}</p>
                <button className="w-[70%] mt-8 bg-primary-400 text-[2.2rem] text-white rounded-2xl py-3 px-8 no-underline border-none shadow-button cursor-pointer mr-20 font-poppins font-medium md:mt-4 md:text-2xl md:rounded-[1.3rem]" onClick={handleRedirect}>
                  {t("common.getStarted")}
                </button>
              </div>
              <div className="w-1/2 text-white">
                <Image
                  alt={t("slides.reading.imageAlt")}
                  src={SideIris}
                  className="w-full h-full"
                  priority
                />
              </div>
            </div>
          ) : (
            <div className="w-full h-[600px] bg-[url('/images/product/mobile_iris.webp')] bg-cover bg-center bg-no-repeat flex object-contain">
              <div className="flex flex-col items-center text-white my-0 mx-auto">
                <h2 className="text-[2rem] mb-2">{t("slides.reading.title")}</h2>
                <p className="my-2 mx-2 text-xl text-center font-poppins">{t("slides.reading.description")}</p>
                <button className="font-poppins font-medium mt-6 bg-primary-400 text-2xl text-white rounded-2xl py-3 px-8 no-underline border-none shadow-[0px_10px_6px_rgba(0,0,0,0.978)] cursor-pointer" onClick={handleRedirect}>
                  {t("common.getStarted")}
                </button>
              </div>
            </div>
          )}
        </SwiperSlide>

        <SwiperSlide>
          {!isMobile ? (
            <div className="max-w-full bg-[url('/images/product/sel.webp')] bg-[#b0f1fb] bg-cover bg-center bg-no-repeat flex object-cover">
              <div className="flex justify-center flex-col px-16 flex-wrap box-border w-1/2 text-white md:px-4">
                <h2 className="text-[5rem] m-0 md:text-[3rem]">{t("slides.emotional.title")}</h2>
                <p className="text-2xl font-poppins md:text-[1.1rem]">{t("slides.emotional.description")}</p>
                <button className="w-[70%] mt-8 bg-primary-400 text-[2.2rem] text-white rounded-2xl py-3 px-8 no-underline border-none shadow-button cursor-pointer mr-20 font-poppins font-medium md:mt-4 md:text-2xl md:rounded-[1.3rem]" onClick={handleRedirect}>
                  {t("common.getStarted")}
                </button>
              </div>
              <div className="w-1/2 text-white">
                <Image
                  alt={t("slides.emotional.imageAlt")}
                  src={SideSel}
                  className="w-full h-full"
                  priority
                />
              </div>
            </div>
          ) : (
            <div className="w-full h-[600px] bg-[url('/images/product/mobile_sel.webp')] bg-cover bg-center bg-no-repeat flex object-contain">
              <div className="flex flex-col items-center text-white my-0 mx-auto">
                <h2 className="text-[2rem] mb-2">{t("slides.emotional.title")}</h2>
                <p className="my-2 mx-2 text-xl text-center font-poppins">{t("slides.emotional.description")}</p>
                <button className="font-poppins font-medium mt-6 bg-primary-400 text-2xl text-white rounded-2xl py-3 px-8 no-underline border-none shadow-[0px_10px_6px_rgba(0,0,0,0.978)] cursor-pointer" onClick={handleRedirect}>
                  {t("common.getStarted")}
                </button>
              </div>
            </div>
          )}
        </SwiperSlide>

        <SwiperSlide>
          {!isMobile ? (
            <div className="max-w-full bg-[url('/images/product/tracing.webp')] bg-[#02c7a5] bg-cover bg-center bg-no-repeat flex object-cover">
              <div className="flex justify-center flex-col px-16 flex-wrap box-border w-1/2 text-white md:px-4">
                <h2 className="text-[5rem] m-0 md:text-[3rem]">{t("slides.tracing.title")}</h2>
                <p className="text-2xl font-poppins md:text-[1.1rem]">{t("slides.tracing.description")}</p>
                <button className="w-[70%] mt-8 bg-primary-400 text-[2.2rem] text-white rounded-2xl py-3 px-8 no-underline border-none shadow-button cursor-pointer mr-20 font-poppins font-medium md:mt-4 md:text-2xl md:rounded-[1.3rem]" onClick={handleRedirect}>
                  {t("common.getStarted")}
                </button>
              </div>
              <div className="w-1/2 text-white">
                <Image
                  alt={t("slides.tracing.imageAlt")}
                  src={SideTracing}
                  className="w-full h-full"
                  priority
                />
              </div>
            </div>
          ) : (
            <div className="w-full h-[600px] bg-[url('/images/product/mobile.webp')] bg-cover bg-center bg-no-repeat flex object-contain">
              <div className="flex flex-col items-center text-white my-0 mx-auto">
                <h2 className="text-[2rem] mb-2">{t("slides.tracing.title")}</h2>
                <p className="my-2 mx-2 text-xl text-center font-poppins">{t("slides.tracing.description")}</p>
                <button className="font-poppins font-medium mt-6 bg-primary-400 text-2xl text-white rounded-2xl py-3 px-8 no-underline border-none shadow-[0px_10px_6px_rgba(0,0,0,0.978)] cursor-pointer" onClick={handleRedirect}>
                  {t("common.getStarted")}
                </button>
              </div>
            </div>
          )}
        </SwiperSlide>
      </Swiper>
      <div className="parent-container">
        <div className="custom-pagination"></div>
      </div>
    </>
  );
}
