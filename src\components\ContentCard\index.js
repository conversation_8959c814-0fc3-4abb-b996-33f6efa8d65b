import Image from "next/image";

const ContentCard = ({ 
  title, 
  description, 
  image, 
  imageAlt = "",
  onClick,
  href,
  className = "",
  imageClassName = "",
  titleClassName = "",
  descriptionClassName = "",
  variant = "default" // default, about, blog, news
}) => {
  const cardVariants = {
    default: "card-hover",
    about: "about-card",
    blog: "card-hover max-w-sm",
    news: "card-hover max-w-md"
  };

  const imageVariants = {
    default: "",
    about: "about-card-image",
    blog: "w-full h-48 object-cover rounded-t-lg",
    news: "w-full h-56 object-cover rounded-t-lg"
  };

  const titleVariants = {
    default: "heading-quaternary",
    about: "about-card-text",
    blog: "heading-quaternary mt-4",
    news: "heading-quaternary mt-4"
  };

  const descriptionVariants = {
    default: "text-body mt-2",
    about: "text-body mt-2",
    blog: "text-body-sm mt-2",
    news: "text-body mt-2"
  };

  const CardWrapper = ({ children }) => {
    if (href) {
      return (
        <a href={href} className={`${cardVariants[variant]} ${className}`}>
          {children}
        </a>
      );
    }
    
    if (onClick) {
      return (
        <div 
          role="button"
          tabIndex={0}
          onClick={onClick}
          onKeyDown={(e) => {
            if (e.key === 'Enter' || e.key === ' ') {
              onClick();
            }
          }}
          className={`${cardVariants[variant]} cursor-pointer ${className}`}
        >
          {children}
        </div>
      );
    }

    return (
      <div className={`${cardVariants[variant]} ${className}`}>
        {children}
      </div>
    );
  };

  return (
    <CardWrapper>
      {image && (
        <Image
          src={image}
          alt={imageAlt}
          width={variant === "about" ? 330 : 400}
          height={variant === "about" ? 230 : 300}
          className={`${imageVariants[variant]} ${imageClassName}`}
        />
      )}
      {title && (
        <h3 className={`${titleVariants[variant]} ${titleClassName}`}>
          {title}
        </h3>
      )}
      {description && (
        <p className={`${descriptionVariants[variant]} ${descriptionClassName}`}>
          {description}
        </p>
      )}
    </CardWrapper>
  );
};

export default ContentCard;
