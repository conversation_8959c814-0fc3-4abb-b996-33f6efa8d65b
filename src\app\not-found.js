"use client";
import Image from "next/image";
import Link from "next/link";
import moyoImage from "../../public/images/notFound/moyo.png";
// import styles from "./not-found.module.css";
const PageNotFound = () => {
  return (
    <div className="max-w-container mx-auto w-full h-full bg-cover bg-center bg-repeat bg-[url('data:image/svg+xml;utf8,%3Csvg%20viewBox%3D%220%200%202000%201400%22%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%3E%3Cdefs%3E%3Cfilter%20id%3D%22b%22%20x%3D%22-200%25%22%20y%3D%22-200%25%22%20width%3D%22500%25%22%20height%3D%22500%25%22%3E%3CfeGaussianBlur%20in%3D%22SourceGraphic%22%20stdDeviation%3D%2220%22/%3E%3C/filter%3E%3C/defs%3E%3Cpath%20fill%3D%22%23000336%22%20d%3D%22M0%200h2000v1400H0z%22/%3E%3C/svg%3E')] pt-5 text-white flex justify-center items-center flex-col text-center">
      <h1 className="font-black text-[5rem] m-0 md:text-[3rem] max-[480px]:text-[2.5rem]">404</h1>
      <p className="font-poppins text-[2rem] my-[10px] mx-0 md:text-[2rem] max-[480px]:text-[0.9rem]">
        Oops, We couldn’t find the page you were looking for
      </p>
      <Link href="/">
        <button className="py-[15px] px-[90px] rounded-lg text-[2rem] cursor-pointer bg-transparent text-white border border-white md:px-[70px] max-[480px]:text-[0.9rem] max-[480px]:py-[10px] max-[480px]:px-[40px]">
          Go Home
        </button>
      </Link>
      <Image
        src={moyoImage}
        alt="Moyo"
        className="mt-[60px] h-[300px] w-[300px] max-[480px]:w-[284px] max-[480px]:h-[244px] max-[480px]:mt-[100px]"
      />
    </div>
  );
};
export default PageNotFound;
