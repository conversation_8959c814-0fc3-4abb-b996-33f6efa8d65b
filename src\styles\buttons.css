/* Button Components */

/* Button Components - Using Global Font System */
.btn-primary {
  @apply bg-primary-400 text-white font-nevermind-bold text-lg font-bold px-6 py-3 rounded-xl
         shadow-button-inset border-none cursor-pointer transition-transform duration-200
         hover:-translate-y-1;
}

.btn-secondary {
  @apply bg-white text-brand-purple-dark font-nevermind-bold text-lg font-bold px-6 py-3 rounded-xl
         border border-gray-300 cursor-pointer transition-all duration-200
         hover:bg-gray-50 hover:shadow-card-hover;
}

.btn-outline {
  @apply bg-transparent text-black font-nevermind-bold text-lg font-bold px-6 py-3 rounded-xl
         border border-black cursor-pointer transition-all duration-200
         hover:bg-black hover:text-white;
}

.btn-small {
  @apply font-nevermind-bold text-sm font-bold px-4 py-2 rounded-lg
         cursor-pointer transition-all duration-200;
}

.btn-large {
  @apply font-nevermind-bold text-xl font-bold px-8 py-4 rounded-xl
         cursor-pointer transition-all duration-200;
}

.btn-cta {
  @apply mt-4 bg-primary-400 text-3xl font-medium text-white rounded-2xl py-4 px-12
         border-none shadow-button cursor-pointer font-poppins transition-transform duration-200
         hover:-translate-y-1 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:translate-y-0
         max-[1023px]:text-[1.75rem] max-[767px]:text-[1.3rem] max-[767px]:rounded-[0.6rem]
         max-[767px]:py-2 max-[767px]:px-8;
}

/* Filter Components */
.btn-filter {
  @apply rounded-[40px] border border-[#d2d2d2] text-[#737373] py-2 px-4 whitespace-nowrap cursor-pointer
         hover:bg-[#fc8800] hover:text-white hover:border-transparent transition-colors duration-200;
}

.btn-filter-active {
  @apply bg-[#fc8800] text-white border-transparent;
}
