"use client";

import parse from "html-react-parser";
import Image from "next/image";
import { useEffect, useState } from "react";
// import 'react-loading-skeleton/dist/skeleton.css'
import Loader from "@/components/Loader";
import { useTranslations } from "next-intl";

const ArticleDetails = ({ params }) => {
  const [articleData, setArticledata] = useState([]);
  const [isLoading, setLoading] = useState(true);
  const t = useTranslations("News");

  useEffect(() => {
    const getPostData = async () => {
      const data = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/wp-json/wp/v2/news?slug=${params?.newsID}`
      );
      const result = await data.json();
      setArticledata(result[0]);
      setLoading(false);
    };
    getPostData();
  }, [params?.newsID]);

  const formatedDate = (dateStr) => {
    const date = new Date(dateStr);
    if (isNaN(date.getTime())) {
      console.error("Invalid date:", dateStr);
      return "Invalid Date";
    }
    const options = { year: "numeric", month: "long", day: "numeric" };
    return new Intl.DateTimeFormat("en-US", options).format(date);
  };

  function removeVCTags(content) {
    if (!content) return ""; // Prevent null or undefined input
    const regex = /\[\/?vc_[^\]]*\]/g;
    const newContent = content.replace(regex, "");
    const regex2 = /(JT[A-Za-z0-9%]{18,}=*)/g;
    return newContent.replace(regex2, "");
  }

  const BlogContent = ({ content }) => {
    const renderedContent = removeVCTags(content);
    const parsedContent = parse(renderedContent, {
      replace: (domNode) => {
        if (domNode.name === "script" || domNode.name === "style") {
          return null;
        }
      },
    });
    return <div>{parsedContent}</div>;
  };

  const Heading = ({ content }) => {
    return <h1 dangerouslySetInnerHTML={{ __html: content }} />;
  };

  const imageUrl = articleData?.yoast_head_json?.schema?.["@graph"]?.[1]?.url || "/default.jpg";

  return isLoading ? (
    <Loader />
  ) : (
    <>
      <div className="blog-detail-container">
        <div className="blog-detail-image">
          <Image
            alt={articleData?.yoast_head_json?.og_site_name}
            src={imageUrl}
            width={975}
            height={300}
            className="max-w-full max-h-full object-contain"
          />
        </div>
        <Heading content={articleData?.title?.rendered} />
        <div className="blog-detail-meta">
          <p className="text-body">{t("teamSkidos")}</p>
          <p className="text-body">|</p>
          <p className="text-body"> {formatedDate(articleData?.date)}</p>
          <p className="text-body">|</p>
          <p className="text-body">
            {" "}
            {t("readingTime")}:{" "}
            {articleData?.yoast_head_json?.twitter_misc &&
              articleData?.yoast_head_json?.twitter_misc["Estimated reading time"]}
          </p>
        </div>
        {articleData?.content?.rendered && <BlogContent content={articleData?.content?.rendered} />}
      </div>
    </>
  );
};

export default ArticleDetails;
