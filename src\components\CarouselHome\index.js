"use client";
import { useAuth } from "@/context/AuthContext";
import { trackWebEngageEvent } from "@/utils/webengage";
import { useTranslations } from "next-intl";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
// import styles from "./styles.module.css";

const Carousel = () => {
  const t = useTranslations("Carousel");
  const [currentSlide, setCurrentSlide] = useState(0);
  const [touchStartX, setTouchStartX] = useState(0);
  const [touchEndX, setTouchEndX] = useState(0);
  const [touchStartY, setTouchStartY] = useState(0);
  const [touchEndY, setTouchEndY] = useState(0);
  const { isLoggedIn, isSubscribed } = useAuth();
  const router = useRouter();

  const carouselData = [
    {
      bannerImage: "/images/webGl/webGlBanner/banner.webp",
      bannerHeading: t("slides.slide1.heading"),
      bannerSubheading: t("slides.slide1.subheading"),
      bannerChildImg: "/images/webGl/webGlBanner/webGlBannerChild.webp",
      redirection: "/get-started",
    },
    {
      bannerImage: "/images/carouselBackground.webp",
      bannerHeading: t("slides.slide2.heading"),
      bannerSubheading: t("slides.slide2.subheading"),
      bannerChildImg: "/images/carouselBannerChild.webp",
      redirection: "/get-started",
    },
    {
      bannerImage: "/images/carouselBackground3.webp",
      bannerHeading: t("slides.slide3.heading"),
      bannerSubheading: t("slides.slide3.subheading"),
      bannerChildImg: "/images/carouselBannerChild3.webp",
      redirection: "/get-started",
    },
    {
      bannerImage: "/images/carouselBackground2.webp",
      bannerHeading: t("slides.slide4.heading"),
      bannerSubheading: t("slides.slide4.subheading"),
      bannerChildImg: "/images/carouselBannerChild2.webp",
      redirection: "/get-started",
    },
  ];

  const handleTouchStart = (e) => {
    setTouchStartX(e.touches[0].clientX);
    setTouchStartY(e.touches[0].clientY);
  };

  const handleTouchMove = (e) => {
    setTouchEndX(e.touches[0].clientX);
    setTouchEndY(e.touches[0].clientY);
  };

  const handleTouchEnd = () => {
    const horizontalSwipeDistance = touchStartX - touchEndX;
    const verticalSwipeDistance = touchStartY - touchEndY;

    if (Math.abs(verticalSwipeDistance) < 50) {
      if (horizontalSwipeDistance > 75) {
        setCurrentSlide((prev) => (prev === carouselData.length - 1 ? 0 : prev + 1));
      } else if (horizontalSwipeDistance < -75) {
        setCurrentSlide((prev) => (prev === 0 ? carouselData.length - 1 : prev - 1));
      }
    }

    setTouchStartX(0);
    setTouchEndX(0);
    setTouchStartY(0);
    setTouchEndY(0);
  };

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev === carouselData.length - 1 ? 0 : prev + 1));
    }, 10000);
    return () => clearInterval(interval);
  }, []);

  const handleStartLearningClick = (redirectPath) => {
    trackWebEngageEvent("WebGLBannerClk");
    router.push(isSubscribed ? "/user-home-screen" : redirectPath);
  };

  return (
    <div className="relative w-full overflow-hidden">
      <div
        className="flex transition-transform duration-300 ease-in-out w-full"
        style={{ transform: `translateX(-${currentSlide * 100}%)` }}
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
      >
        {carouselData.map((item, index) => (
          <div
            key={index}
            className={`flex-none w-full h-[600px] bg-cover bg-center ${currentSlide === index ? "opacity-100" : ""}`}
            style={{ backgroundImage: `url(${item.bannerImage})` }}
          >
            <div className="flex items-center h-full max-[767px]:flex-col">
              <div className="w-[55%] px-20 box-border max-[1023px]:px-4 max-[767px]:w-full max-[767px]:px-4 max-[767px]:py-6 max-[767px]:text-center">
                <h1 className="text-white text-5xl m-0 max-[1023px]:text-[2.5rem] max-[767px]:text-[1.8rem]">{item.bannerHeading}</h1>
                <p className="my-3 mx-0 text-white text-2xl font-poppins font-medium max-[1023px]:text-xl max-[767px]:text-[0.9rem]">{item.bannerSubheading}</p>
                <button
                  className="mt-4 bg-primary-400 text-3xl font-medium text-white rounded-2xl py-4 px-12 no-underline border-none shadow-button cursor-pointer font-poppins max-[1023px]:text-[1.75rem] max-[767px]:text-[1.3rem] max-[767px]:rounded-[0.6rem] max-[767px]:py-2 max-[767px]:px-8"
                  onClick={() => handleStartLearningClick(item.redirection)}
                >
                  {t("buttons.startLearning")}
                </button>
              </div>
              <div className="relative w-[45%] h-full max-[767px]:w-full">
                <Image
                  src={item.bannerChildImg}
                  layout="fill"
                  objectFit="contain"
                  objectPosition="center"
                  priority
                  alt={t("imageAlts.childParent")}
                />
              </div>
            </div>
          </div>
        ))}
      </div>
      <div className="absolute bottom-[30px] left-1/2 transform -translate-x-1/2 flex border border-white p-[6px] rounded-[40px] box-border z-50">
        {carouselData.map((_, index) => (
          <span
            key={index}
            className={`w-2 h-2 bg-white opacity-100 rounded-full transition-all duration-[0.9s] mx-[5px] bg-white/30 cursor-pointer ${currentSlide === index ? "w-10 h-2 bg-white rounded-[24px]" : ""}`}
            onClick={() => setCurrentSlide(index)}
          />
        ))}
      </div>
    </div>
  );
};

export default Carousel;
