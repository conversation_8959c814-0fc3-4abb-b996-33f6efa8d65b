"use client";
import useIsMobile from "@/hooks/useIsMobile";
import { gsap } from "gsap";
import { ScrollToPlugin } from "gsap/ScrollToPlugin";
import { ScrollTrigger } from "gsap/dist/ScrollTrigger";
import Image from "next/image";
import Link from "next/link";
import { useEffect, useRef } from "react";
// import styles from "./styles.module.css";
import { themeCardsRow1, themeCardsRow2 } from "@/constants";
import useScrollAnimation from "@/hooks/useScrollAnimation";

gsap.registerPlugin(ScrollToPlugin, ScrollTrigger);

const scrollAnimation = (wrapper, card) => {
  gsap.fromTo(
    card,
    { opacity: 0, y: 50 },
    {
      opacity: 1,
      y: 0,
      duration: 0.5,
      ease: "power2.out",
      stagger: 0.5,
      scrollTrigger: {
        trigger: wrapper,
        start: "top 80%",
        end: "top 50%",
        once: true,
      },
    }
  );
};

const HomeThemeCarouselCt = () => {
  const isMobile = useIsMobile();
  const containerRef = useRef(null);
  const animationRef = useRef(null);

  useEffect(() => {
    scrollAnimation(`.nurturingGrowthCardsWrapper`, `.nurturingGrowthCards`);

    const container = containerRef.current;
    if (!container) return;
    const maxScrollLeft = container.scrollWidth - container.clientWidth;

    animationRef.current = gsap.to(container, {
      scrollTo: { x: maxScrollLeft },
      duration: 10,
      repeat: -1,
      ease: "none",
      yoyo: true,
    });

    container.addEventListener("mouseenter", () => animationRef.current.pause());
    container.addEventListener("mouseleave", () => animationRef.current.play());

    return () => {
      animationRef.current.kill();
      container.removeEventListener("mouseenter", () => animationRef.current.pause());
      container.removeEventListener("mouseleave", () => animationRef.current.play());
    };
  }, []);

  const setRef = useScrollAnimation(
    {
      duration: 1,
      ease: "power3.out",
      start: "top bottom",
    },
    []
  );

  return (
    <div ref={containerRef} className="flex flex-col items-center w-full overflow-auto flex-wrap scrollbar-none mb-8">
      <div className="relative flex">
        {themeCardsRow1.map((theme) => {
          return (
            <Link
              href={{
                pathname: `/products`,
                query: {
                  theme: theme.themeName,
                },
              }}
              className="noLinkStyle"
              key={theme.themeName}
              prefetch={false}
            >
              <div className="bg-white p-[0.8rem] rounded-xl m-[10px] text-[0.8rem] text-center text-[#4a2d80] cursor-pointer h-[210px] md:p-4 md:text-[1.2rem] max-[430px]:h-[210px]">
                <Image
                  src={theme.themeImg}
                  alt={theme.themeName}
                  width={230}
                  height={150}
                  className="w-[150px] h-[100px] md:w-[230px] md:h-[150px]"
                  placeholder="blur"
                  blurDataURL="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNctmhZPQAGUwJv7ObBEQAAAABJRU5ErkJggg=="
                />
                <p className="my-2">{theme.themeName}</p>
                <p className="font-poppins text-[0.8rem] text-[#727272] m-0">{theme.themeDesc}</p>
              </div>
            </Link>
          );
        })}
      </div>
      <div className="relative flex">
        {themeCardsRow2.map((theme) => {
          return (
            <Link
              href={{
                pathname: `/products`,
                query: {
                  theme: theme.themeName,
                },
              }}
              className="noLinkStyle"
              key={theme.themeName}
            >
              <div className="bg-white p-[0.8rem] rounded-xl m-[10px] text-[0.8rem] text-center text-[#4a2d80] cursor-pointer h-[210px] md:p-4 md:text-[1.2rem] max-[430px]:h-[210px]">
                <Image
                  src={theme.themeImg}
                  alt={theme.themeName}
                  width={230}
                  height={150}
                  className="w-[150px] h-[100px] md:w-[230px] md:h-[150px]"
                  placeholder="blur"
                  blurDataURL="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNctmhZPQAGUwJv7ObBEQAAAABJRU5ErkJggg=="
                />
                <p className="my-2">{theme.themeName}</p>
                <p className="font-poppins text-[0.8rem] text-[#727272] m-0">{theme.themeDesc}</p>
              </div>
            </Link>
          );
        })}
      </div>
    </div>
  );
};

export default HomeThemeCarouselCt;
