import Image from "next/image";

const PageHeader = ({ 
  title, 
  subtitle, 
  backgroundImage, 
  backgroundImageMobile,
  children,
  className = "",
  titleClassName = "",
  subtitleClassName = "",
  height = "h-[600px] md:h-[400px]"
}) => {
  const bgClass = backgroundImageMobile 
    ? `bg-[url('${backgroundImage}')] md:bg-[url('${backgroundImageMobile}')]`
    : `bg-[url('${backgroundImage}')]`;

  return (
    <header className={`${bgClass} ${height} bg-cover bg-center flex justify-center items-center flex-col container-main ${className}`}>
      <div className="w-full text-white text-center flex flex-col items-center justify-center px-12 md:px-4 md:mt-4 box-border">
        {title && (
          <h1 className={`heading-primary text-white m-0 ${titleClassName}`}>
            {title}
          </h1>
        )}
        {subtitle && (
          <p className={`text-subheading text-white m-0 px-40 md:px-2 box-border ${subtitleClassName}`}>
            {subtitle}
          </p>
        )}
        {children}
      </div>
    </header>
  );
};

export default PageHeader;
