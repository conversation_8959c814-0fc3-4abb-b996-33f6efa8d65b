
const PageHeader = ({
  title,
  subtitle,
  backgroundImage,
  backgroundImageMobile,
  children,
  className = "",
  titleClassName = "",
  subtitleClassName = "",
  height = "h-[600px] md:h-[400px]"
}) => {
  // Use inline styles for dynamic background images to avoid Tailwind CSS issues
  const backgroundStyle = backgroundImage
    ? {
        backgroundImage: `url('${backgroundImage}')`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat'
      }
    : {};

  const mobileBackgroundStyle = backgroundImageMobile
    ? {
        backgroundImage: `url('${backgroundImageMobile}')`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat'
      }
    : {};

  return (
    <>
      {/* Desktop background */}
      <header
        className={`${height} bg-cover bg-center flex justify-center items-center flex-col container-main hidden md:flex ${className}`}
        style={backgroundStyle}
      >
        <div className="w-full text-white text-center flex flex-col items-center justify-center px-12 md:px-4 md:mt-4 box-border">
          {title && (
            <h1 className={`heading-primary text-white m-0 ${titleClassName}`}>
              {title}
            </h1>
          )}
          {subtitle && (
            <p className={`text-subheading text-white m-0 px-40 md:px-2 box-border ${subtitleClassName}`}>
              {subtitle}
            </p>
          )}
          {children}
        </div>
      </header>

      {/* Mobile background */}
      <header
        className={`${height} bg-cover bg-center flex justify-center items-center flex-col container-main md:hidden ${className}`}
        style={backgroundImageMobile ? mobileBackgroundStyle : backgroundStyle}
      >
        <div className="w-full text-white text-center flex flex-col items-center justify-center px-12 md:px-4 md:mt-4 box-border">
          {title && (
            <h1 className={`heading-primary text-white m-0 ${titleClassName}`}>
              {title}
            </h1>
          )}
          {subtitle && (
            <p className={`text-subheading text-white m-0 px-40 md:px-2 box-border ${subtitleClassName}`}>
              {subtitle}
            </p>
          )}
          {children}
        </div>
      </header>
    </>
  );
};

export default PageHeader;
