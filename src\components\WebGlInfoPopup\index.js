"use client";
import { useTranslations } from "next-intl";
import { useEffect, useState } from "react";
// import styles from "./styles.module.css";

const InfoPopup = ({ isOpen, onClose, data, localeData }) => {
  const t = useTranslations(localeData);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    if (isOpen) {
      document.body.classList.add("overflow-hidden");
      // Delay to trigger animation
      setTimeout(() => setIsVisible(true), 10);
    } else {
      setIsVisible(false);
      // Wait for animation to complete before removing from DOM
      setTimeout(() => {
        document.body.classList.remove("overflow-hidden");
      }, 400);
    }
    return () => {
      document.body.classList.remove("overflow-hidden");
    };
  }, [isOpen]);

  const handleClose = () => {
    setIsVisible(false);
    setTimeout(onClose, 400);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed top-0 left-0 w-screen h-screen bg-black bg-opacity-70 flex justify-center items-center z-[1000] animate-fadeIn">
      <div className={`bg-[#fdf8f5] max-w-[400px] rounded-[15px] p-5 text-left relative shadow-[0_4px_20px_rgba(0,0,0,0.2)] transition-all duration-400 ease-in-out ${isVisible ? "translate-y-0 opacity-100" : "translate-y-full opacity-0"}`}>
        <img src={data.bannerImage} alt="Parent and child" className="w-full h-auto rounded-[10px] mb-2" />
        <h2 className="modal-title text-gray-800 m-0">{t(data.heading)}</h2>
        <ul className="list-none p-0 m-0 mb-5">
          {data?.popupContent.map((key, index) => (
            <li key={index} className="text-body text-gray-500 my-[10px] mx-0 flex before:content-['▶'] before:text-gray-600 before:mr-2">{t(key)}</li>
          ))}
        </ul>
        <button className="btn-primary btn-large mt-4 w-full" onClick={handleClose}>
          {t("PopCta")}
        </button>
      </div>
    </div>
  );
};

export default InfoPopup;
