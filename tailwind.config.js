/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      colors: {
        // Primary brand colors
        primary: {
          50: '#f3f0ff',
          100: '#e9e5ff',
          200: '#d6cfff',
          300: '#b8a9ff',
          400: '#9258fe', // Main primary color
          500: '#8b5cf6',
          600: '#7c3aed',
          700: '#6d28d9',
          800: '#5b21b6',
          900: '#4c1d95',
        },
        secondary: {
          50: '#f0fdf4',
          100: '#dcfce7',
          200: '#bbf7d0',
          300: '#86efac',
          400: '#4ade80',
          500: '#1dc368', // Main secondary green
          600: '#16a34a',
          700: '#15803d',
          800: '#166534',
          900: '#14532d',
        },
        accent: {
          50: '#fdf4ff',
          100: '#fae8ff',
          200: '#f5d0fe',
          300: '#f0abfc',
          400: '#ffc3ff', // Main accent pink
          500: '#d946ef',
          600: '#c026d3',
          700: '#a21caf',
          800: '#86198f',
          900: '#701a75',
        },
        // Custom brand colors
        brand: {
          purple: '#6828ee',
          'purple-light': '#955dff',
          'purple-dark': '#4a2d80',
          green: '#1dc368',
          pink: '#ffc3ff',
          'bg-cream': '#f7f0ea',
          'bg-light': '#f7f8fe',
          'text-gray': '#595959',
          'text-light': '#727272',
        },
      },
      fontFamily: {
        'nevermind-bold': ['var(--font-nevermind-bold)', 'sans-serif'],
        'nevermind-light': ['var(--font-nevermind-light)', 'sans-serif'],
        'nevermind-medium': ['var(--font-nevermind-medium)', 'sans-serif'],
        'poppins': ['var(--font-poppins)', 'sans-serif'],
      },
      maxWidth: {
        'container': '1728px',
      },
      spacing: {
        '26': '6.5rem',
      },
      borderRadius: {
        'xl': '20px',
      },
      boxShadow: {
        'button': '0px 10px 0px rgba(74, 45, 128, 1)',
        'button-inset': 'inset 0px 10px 5px rgba(203, 177, 252, 0.9), 0px 10px 6px rgba(0, 0, 0, 0.978)',
        'card': '0 0 12px rgba(0, 0, 0, 0.15)',
      },
      backdropBlur: {
        'xs': '2px',
      },
      animation: {
        'spin-slow': 'spin 1.6s linear infinite',
        'shimmer': 'shimmer 2.2s linear infinite',
        'shootingStar': 'shootingStar 3s ease-in-out infinite',
        'fadeIn': 'fadeIn 0.3s ease-out',
      },
      keyframes: {
        shimmer: {
          '0%': { backgroundPosition: '-1200px 0' },
          '100%': { backgroundPosition: '1200px 0' },
        },
        shootingStar: {
          '0%': { transform: 'translate(-10%, -10%) rotate(0deg)', opacity: '1' },
          '50%': { transform: 'translate(100vw, 100vh) rotate(45deg)', opacity: '1' },
          '75%': { transform: 'translate(50vw, 50vh) rotate(90deg)', opacity: '0.5' },
          '100%': { transform: 'translate(-10%, -10%) rotate(360deg)', opacity: '0' },
        },
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
      },
    },
  },
  plugins: [],
}
