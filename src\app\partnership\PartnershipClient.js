"use client";
import useIsMobile from "@/hooks/useIsMobile";
import useScrollAnimation from "@/hooks/useScrollAnimation";
import { useTranslations } from "next-intl";
import Image from "next/image";
import { useEffect } from "react";
import Mascots from "../../../public/images/contactUs/contactUsMascots.webp";

const PartnershipClient = () => {
  const t = useTranslations("Partnership");
  const isMobile = useIsMobile();
  const setRef = useScrollAnimation(
    {
      duration: 1,
      ease: "power3.out",
      start: "top bottom",
    },
    []
  );
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);
  return (
    <>
      <div className="max-w-[1728px] mx-auto">
        <header className="flex justify-center items-center py-8 px-4 text-[3rem] gap-5 flex-col md:flex-row">
          <div ref={(el) => setRef(el)} className="md:px-12 md:my-auto md:block md:my-auto md:text-left">
            <h2 className="text-[3rem] m-0">{t("title")}</h2>
            {isMobile ? (
              <p className="text-[1.5rem]">{t("subtitle.mobile")}</p>
            ) : (
              <p className="text-[1.5rem]">
                {t("subtitle.desktop1")}
                <br />
                {t("subtitle.desktop2")}
              </p>
            )}
            <button className="bg-[#9258fe] text-[1.2rem] text-white rounded-[0.8rem] py-[0.8rem] px-[1.5rem] no-underline border-none shadow-[inset_0px_5px_5px_rgba(203,177,252,0.9),0px_5px_6px_rgba(0,0,0,0.978)] cursor-pointer mr-20 md:m-0 md:py-[0.8rem] md:px-8">
              <a
                className="text-white no-underline"
                href="mailto:<EMAIL>"
                target="_blank"
                rel="noopener noreferrer"
                data-stringify-link="mailto:<EMAIL>"
                data-sk="tooltip_parent"
                aria-haspopup="menu"
                aria-expanded="false"
              >
                {t("collaborate")}
              </a>
            </button>
          </div>
          <div ref={(el) => setRef(el)} className="w-[320px] h-[320px]">
            <Image alt={t("mascotsAlt")} src={Mascots} className="w-full h-full object-contain" />
          </div>
        </header>
        <section className="text-center py-8 px-4 md:p-12" ref={(el) => setRef(el)}>
          <h1 className="text-[2.5rem]">{t("ourPartners")}</h1>
          <div className="flex flex-col items-center gap-8 md:flex-row md:justify-center">
            <Image
              alt={t("mitgameAlt")}
              src="/images/partnership/mitgame.png"
              width={isMobile ? 250 : 413}
              height={isMobile ? 120 : 200}
            />
            <Image
              alt={t("prodegeAlt")}
              src="/images/partnership/prodege.png"
              width={isMobile ? 200 : 360}
              height={isMobile ? 70 : 100}
            />
          </div>
        </section>
        <section className="text-center py-8 px-4" ref={(el) => setRef(el)}>
          <h1 className="text-[2.5rem]">{t("waysCollaborate")}</h1>
          <div className="flex justify-around items-center gap-5 flex-wrap">
            <div>
              <div className="bg-[#e0edff] p-14">
                <Image
                  src="/images/partnership/handShake.png"
                  height={147}
                  width={147}
                  alt={t("brandPartnershipAlt")}
                />
              </div>
              <p className="text-center">{t("brandPartnership")}</p>
            </div>
            <div>
              <div className="bg-[#e0edff] p-14">
                <Image
                  src="/images/partnership/certificate.png"
                  height={147}
                  width={147}
                  alt={t("contentLicensingAlt")}
                />
              </div>
              <p className="text-center">{t("contentLicensing")}</p>
            </div>
            <div>
              <div className="bg-[#e0edff] p-14">
                <Image
                  src="/images/partnership/handHeart.png"
                  height={147}
                  width={147}
                  alt={t("csrAlt")}
                />
              </div>
              <p className="text-center">{t("csr")}</p>
            </div>
          </div>
        </section>
      </div>
    </>
  );
};

export default PartnershipClient;
