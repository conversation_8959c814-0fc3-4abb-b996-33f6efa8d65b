"use client";
import useIsMobile from "@/hooks/useIsMobile";
import useScrollAnimation from "@/hooks/useScrollAnimation";
import { useTranslations } from "next-intl";
import Image from "next/image";
import { useEffect } from "react";
import Mascots from "../../../public/images/contactUs/contactUsMascots.webp";

const PartnershipClient = () => {
  const t = useTranslations("Partnership");
  const isMobile = useIsMobile();
  const setRef = useScrollAnimation(
    {
      duration: 1,
      ease: "power3.out",
      start: "top bottom",
    },
    []
  );
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);
  return (
    <>
      <div className="container-main">
        <header className="partnership-header">
          <div ref={(el) => setRef(el)} className="partnership-content">
            <h2 className="heading-primary m-0">{t("title")}</h2>
            {isMobile ? (
              <p className="text-subheading">{t("subtitle.mobile")}</p>
            ) : (
              <p className="text-subheading">
                {t("subtitle.desktop1")}
                <br />
                {t("subtitle.desktop2")}
              </p>
            )}
            <button className="btn-primary partnership-cta-btn">
              <a
                className="partnership-cta-link"
                href="mailto:<EMAIL>"
                target="_blank"
                rel="noopener noreferrer"
                data-stringify-link="mailto:<EMAIL>"
                data-sk="tooltip_parent"
                aria-haspopup="menu"
                aria-expanded="false"
              >
                {t("collaborate")}
              </a>
            </button>
          </div>
          <div ref={(el) => setRef(el)} className="partnership-image">
            <Image alt={t("mascotsAlt")} src={Mascots} className="w-full h-full object-contain" />
          </div>
        </header>
        <section className="partnership-partners" ref={(el) => setRef(el)}>
          <h1 className="heading-secondary">{t("ourPartners")}</h1>
          <div className="partners-logos">
            <Image
              alt={t("mitgameAlt")}
              src="/images/partnership/mitgame.png"
              width={isMobile ? 250 : 413}
              height={isMobile ? 120 : 200}
            />
            <Image
              alt={t("prodegeAlt")}
              src="/images/partnership/prodege.png"
              width={isMobile ? 200 : 360}
              height={isMobile ? 70 : 100}
            />
          </div>
        </section>
        <section className="partnership-collaboration" ref={(el) => setRef(el)}>
          <h1 className="heading-secondary">{t("waysCollaborate")}</h1>
          <div className="collaboration-options">
            <div className="collaboration-option">
              <div className="collaboration-icon">
                <Image
                  src="/images/partnership/handShake.png"
                  height={147}
                  width={147}
                  alt={t("brandPartnershipAlt")}
                />
              </div>
              <p className="collaboration-text">{t("brandPartnership")}</p>
            </div>
            <div className="collaboration-option">
              <div className="collaboration-icon">
                <Image
                  src="/images/partnership/certificate.png"
                  height={147}
                  width={147}
                  alt={t("contentLicensingAlt")}
                />
              </div>
              <p className="collaboration-text">{t("contentLicensing")}</p>
            </div>
            <div className="collaboration-option">
              <div className="collaboration-icon">
                <Image
                  src="/images/partnership/handHeart.png"
                  height={147}
                  width={147}
                  alt={t("csrAlt")}
                />
              </div>
              <p className="collaboration-text">{t("csr")}</p>
            </div>
          </div>
        </section>
      </div>
    </>
  );
};

export default PartnershipClient;
