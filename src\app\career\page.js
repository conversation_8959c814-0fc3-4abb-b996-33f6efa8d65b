"use client";
import useTogglePinkFooter from "@/hooks/useTogglePinkFooter";
import { faArrowRight, faBriefcase, faLocationDot } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { useTranslations } from "next-intl";
import Image from "next/image";
import Carousel from "../../components/CarouselCareer";

function Page() {
  const t = useTranslations("careers");

  const jobs = [
    {
      title: "Senior Software Engineer (Golang)",
      exp: "2+ years of experience",
      location: "Delhi, Noida",
      applyURL: "https://skidos.bamboohr.com/careers/115",
    },
  ];

  const JobCard = ({ job }) => {
    return (
      <div className="bg-white p-6 rounded-lg shadow-md border border-[#e0e0e0] transition-all duration-300 hover:shadow-lg hover:border-[#9258fe]">
        <h2 className="text-[1.5rem] font-bold mb-4 text-[#333] font-nevermind-bold">{job.title}</h2>
        <div className="flex items-center mb-2 text-[#666] font-poppins">
          <span className="mr-5">
            <FontAwesomeIcon icon={faBriefcase} />
          </span>
          {job.exp}
        </div>
        <div className="flex items-center mb-4 text-[#666] font-poppins">
          <span className="mr-5">
            <FontAwesomeIcon icon={faLocationDot} />
          </span>
          {job.location}
        </div>
        <a href={job.applyURL} target="_blank" rel="noreferrer">
          <button className="bg-black text-white border-none rounded cursor-pointer no-underline inline-block transition-[background-color,transform] duration-300 ease font-poppins text-[20px] font-medium leading-[30px] mt-8 py-2 px-4 hover:bg-[#6f42c1] hover:transform hover:translate-x-1">
            {t("apply_now")}
            <span className="ml-2 transition-transform duration-300 ease">
              <FontAwesomeIcon icon={faArrowRight} />
            </span>
          </button>
        </a>
      </div>
    );
  };

  useTogglePinkFooter(true);

  return (
    <>
      <title>{t("title")}</title>
      <meta name="description" content={t("meta_description")} />
      <div className="max-w-[1728px] mx-auto">
        <Carousel />
        {jobs.length > 0 && (
          <div className="py-16 px-8">
            <h1 className="text-center text-[2.5rem] mb-8 font-nevermind-bold">{t("job_available")}</h1>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-6xl mx-auto">
              {jobs.map((job, index) => (
                <JobCard key={index} job={job} />
              ))}
            </div>
          </div>
        )}
        <div>
          <div>
            <div className="card-base text-center bg-[#eaeaea] py-14 px-0 my-8 mx-16 text-[#595959]">
              <Image src="/images/career/suitcase.webp" height={40} width={72} />
              {jobs.length > 0 ? <h2 className="heading-tertiary mt-2 text-black">{t("join_us")}</h2> : <h2 className="heading-tertiary mt-2 text-black">{t("jobs_coming_soon")}</h2>}
              <p className="text-body-lg">{t("company_mission")}</p>
              <button
                className="btn-primary flex items-center gap-[10px] my-0 mx-auto shadow-[0px_8.7px_0px_0px_#004e76]"
                onClick={() =>
                  (window.location.href =
                    "https://www.linkedin.com/company/9246535/admin/dashboard/")
                }
              >
                Linkedin <Image src="/images/career/linkedinLogo.webp" height={36} width={36} />
              </button>
            </div>
          </div>
        </div>
        <div className="section-container text-center my-8 mx-16">
          <h1 className="heading-secondary mb-8">{t("explore_perks")}</h1>
          <div className="grid-cards grid-cols-3 md:grid-cols-2">
            {[
              { img: "suitcase.webp", title: "health_coverage", desc: "health_description" },
              { img: "parent.webp", title: "parental_leave", desc: "parental_description" },
              { img: "wellness.webp", title: "wellness", desc: "wellness_description" },
              { img: "growth.webp", title: "growth_opportunities", desc: "growth_description" },
              { img: "leave.webp", title: "leave_policy", desc: "leave_description" },
              { img: "engagement.webp", title: "team_engagement", desc: "engagement_description" },
              {
                img: "collaborate.webp",
                title: "collaborative_culture",
                desc: "collaborative_description",
              },
              { img: "cash.webp", title: "tax_assistance", desc: "tax_description" },
            ].map((item, index) => (
              <div key={index} className="card-hover bg-[#e8dcfffc] text-center p-8">
                <Image src={`/images/career/${item.img}`} width={72} height={40} />
                <h2 className="heading-quaternary mt-2 text-black">{t(item.title)}</h2>
                <p className="text-body-lg text-[#595959]">{t(item.desc)}</p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </>
  );
}

export default Page;
