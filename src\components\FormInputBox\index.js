// import styles from "./styles.module.css";

const FormInputBox = ({ placeholderVal, typeVal, value, onChange, error }) => {
  return (
    <input
      type={typeVal}
      placeholder={error ? error : placeholderVal}
      className={`w-full box-border border-[2.5px] border-gray-200 rounded-[2.2rem] p-6 text-[1.7rem] text-gray-400 mb-8 focus:outline-none placeholder:text-[1.7rem] placeholder:text-gray-400 max-[480px]:p-4 max-[480px]:rounded-[1.2rem] max-[480px]:border-[1.5px] max-[480px]:text-[1.2rem] max-[480px]:placeholder:text-[1.2rem] ${error ? "border-red-500 text-red-500 bg-red-50 placeholder:text-red-500 max-[480px]:border-[1.5px] max-[480px]:border-red-500" : ""}`}
      value={value}
      onChange={onChange}
    />
  );
};
export default FormInputBox;
