// import styles from "./styles.module.css";

const FormInputBox = ({ placeholderVal, typeVal, value, onChange, error }) => {
  return (
    <input
      type={typeVal}
      placeholder={error ? error : placeholderVal}
      className={`${error ? "form-input-error bg-red-50" : "form-input"} box-border border-[2.5px] rounded-[2.2rem] p-6 text-subheading mb-8 max-[480px]:p-4 max-[480px]:rounded-[1.2rem] max-[480px]:border-[1.5px]`}
      value={value}
      onChange={onChange}
    />
  );
};
export default FormInputBox;
