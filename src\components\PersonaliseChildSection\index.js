"use client";
import FormCtaButton from "@/components/FormCtaButton";
import { inforPopupData } from "@/constants";
import { usePlayerContext } from "@/context/CreatePlayerContext";
import apiClient from "@/utils/axiosUtil";
import { getLocale } from "@/utils/helperFunctions";
import { useLocale, useTranslations } from "next-intl";
import Image from "next/image";
import { useEffect, useState } from "react";
import InfoPopup from "../WebGlInfoPopup";
// import styles from "./styles.module.css";

const cache = {
  themesData: null,
};

const PersonaliseChildSection = ({ onSubmit }) => {
  const { playerData, updatePlayerData } = usePlayerContext();
  const [themesData, setThemeData] = useState([]);
  const [selectedThemes, setSelectedThemes] = useState(playerData.selectedThemes || []);
  const [showPopup, setShowPopup] = useState(false);
  const [error, setError] = useState("");
  const locale = useLocale();
  const lang = getLocale(locale);
  const t = useTranslations("PersonalisePlayer");

  const fetchInterests = async () => {
    try {
      const url = `${process.env.NEXT_PUBLIC_PRODUCTSERVICE_BASE_URL}/interest?l=${lang}&gameid=superstore&version=8.0&platform=ios`;
      const response = await apiClient.get(url);
      setThemeData(response.data);

      // Cache the data and locale
      cache.themesData = response.data;
    } catch (error) {
      console.error("Error fetching interests:", error);
    }
  };
  useEffect(() => {
    if (cache.themesData) {
      setThemeData(cache.themesData);
    } else {
      fetchInterests();
    }
  }, [locale]);

  const handleThemeToggle = (interestId) => {
    setSelectedThemes((prevSelectedThemes) => {
      const updatedThemes = prevSelectedThemes.includes(interestId)
        ? prevSelectedThemes.filter((id) => id !== interestId)
        : [...prevSelectedThemes, interestId];

      updatePlayerData({ selectedThemes: updatedThemes });
      return updatedThemes;
    });
    setError("");
  };

  const handleSubmit = () => {
    if (selectedThemes.length === 0) {
      setError("Please select at least one theme to proceed.");
      return;
    }
    onSubmit();
  };

  return (
    <div>
      <header className="p-4 text-left">
        <h1 className="m-0 text-[2rem]">{t("Heading")}</h1>
        <p className="m-0 font-poppins text-[#6c757d] text-[1.2rem]">{t("SubHeading")}</p>
      </header>
      {themesData && (
        <main className="mx-auto flex flex-wrap justify-center h-[clamp(300px,52vh,400px)] max-h-[400px] min-h-[300px] overflow-y-auto scrollbar-none max-[750px]:h-[clamp(250px,40vh,350px)]">
          {themesData.map((theme, index) => (
            <div
              key={index}
              className="flex-[0_0_calc(33.33%-2rem)] max-w-[calc(33.33%-2rem)] cursor-pointer p-2 box-border text-center rounded-lg transition-transform duration-200 ease-in-out hover:scale-105"
              onClick={() => handleThemeToggle(theme.InterestID)}
            >
              <Image
                src={theme.PictureUrl}
                className={`w-full h-auto max-w-[clamp(75px,15vw,115px)] rounded-full object-cover ${
                  selectedThemes.includes(theme.InterestID) ||
                  playerData.selectedThemes.includes(theme.InterestID)
                    ? "border-[3px] border-[#9258fe] shadow-[0px_0px_4px_4px_rgba(146,88,254,0.33)]"
                    : ""
                }`}
                width={115}
                height={115}
              />
              <p className="mt-[clamp(0.5rem,1vw,1rem)] text-[clamp(0.8rem,1vw,1rem)] text-[#333]">{theme.DisplayName}</p>
            </div>
          ))}
        </main>
      )}
      {error && (
        <div className="w-3/4 rounded-[5px] bg-[#f8d7ce] font-nevermind-light text-[#dd4a38] mt-2 py-2 px-0 mx-auto">
          <p className="font-nevermind-medium m-0">
            <Image src="/images/webGl/warning.png" height={15} width={15} /> {t("ErrorMsgAll")}
          </p>
        </div>
      )}
      <FormCtaButton text={t("CtaBtn")} onClick={handleSubmit} />
      <p className="text-base mt-4 mb-0 text-[#6c757d] font-poppins cursor-pointer" onClick={() => setShowPopup((prev) => !prev)}>
        <Image src="/images/webGl/normalInfo.png" height={15} width={15} alt="Info" />{" "}
        {t("InfoText")}
      </p>
      {showPopup && (
        <InfoPopup
          isOpen={showPopup}
          onClose={() => setShowPopup((prev) => !prev)}
          data={inforPopupData[1]}
          localeData="PersonalisePlayer"
        />
      )}
    </div>
  );
};
export default PersonaliseChildSection;
