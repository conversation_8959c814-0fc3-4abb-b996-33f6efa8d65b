"use client";
import { useTranslations } from "next-intl";
import { useEffect, useState } from "react";
// import styles from "./styles.module.css";

const CarouselEnterEmail = () => {
  const [currentSlide, setCurrentSlide] = useState(0);
  const [touchStartX, setTouchStartX] = useState(0);
  const [touchEndX, setTouchEndX] = useState(0);
  const [touchStartY, setTouchStartY] = useState(0);
  const [touchEndY, setTouchEndY] = useState(0);
  const t = useTranslations("EnterEmailSection");

  const carouselData = [
    {
      bannerImage: "/images/webGl/emailBanner/2.webp",
      bannerHeading: t("Heading"),
      bannerSubheading: t("SubHeading2"),
    },
    {
      bannerImage: "/images/webGl/emailBanner/1.webp",
      bannerHeading: t("Heading"),
      bannerSubheading: t("SubHeading1"),
    },
    {
      bannerImage: "/images/webGl/emailBanner/3.webp",
      bannerHeading: t("Heading"),
      bannerSubheading: t("SubHeading3"),
    },
  ];

  const handleTouchStart = (e) => {
    setTouchStartX(e.touches[0].clientX);
    setTouchStartY(e.touches[0].clientY);
  };

  const handleTouchMove = (e) => {
    setTouchEndX(e.touches[0].clientX);
    setTouchEndY(e.touches[0].clientY);
  };

  const handleTouchEnd = () => {
    const horizontalSwipeDistance = touchStartX - touchEndX;
    const verticalSwipeDistance = touchStartY - touchEndY;

    if (Math.abs(verticalSwipeDistance) < 50) {
      if (horizontalSwipeDistance > 75) {
        setCurrentSlide((prev) => (prev === carouselData.length - 1 ? 0 : prev + 1));
      } else if (horizontalSwipeDistance < -75) {
        setCurrentSlide((prev) => (prev === 0 ? carouselData.length - 1 : prev - 1));
      }
    }

    setTouchStartX(0);
    setTouchEndX(0);
    setTouchStartY(0);
    setTouchEndY(0);
  };

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev === carouselData.length - 1 ? 0 : prev + 1));
    }, 5000);
    return () => clearInterval(interval);
  }, []);

  return (
    <div className="relative w-full h-[360px] overflow-hidden">
      <div className="absolute top-0 left-0 w-full h-full">
        {carouselData.map((_, index) => (
          <div
            key={index}
            className={`absolute top-0 left-0 w-full h-full bg-cover bg-center blur-[8px] scale-110 opacity-0 transition-opacity duration-300 ease-in-out ${currentSlide === index ? "opacity-50" : ""}`}
          />
        ))}
      </div>
      <div
        className="relative flex transition-transform duration-300 ease-in-out w-full h-full z-[1]"
        style={{ transform: `translateX(-${currentSlide * 100}%)` }}
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
      >
        {carouselData.map((item, index) => (
          <div
            key={index}
            className={`relative flex-none w-full h-full flex flex-col items-center justify-center ${currentSlide === index ? "opacity-100" : ""}`}
          >
            <div className="w-full h-full flex items-center justify-center overflow-hidden">
              <img src={item.bannerImage} alt={item.bannerHeading} className="w-full h-full object-contain max-h-[360px]" />
            </div>
          </div>
        ))}
      </div>
      <div className="absolute bottom-[60px] left-0 w-full text-center z-[2] max-[480px]:bottom-20">
        <h1 className="text-base m-0 mb-2 md:text-[1.85rem]">{carouselData[currentSlide].bannerHeading}</h1>
        <p className="m-0 text-sm font-poppins md:text-[0.8rem]">{carouselData[currentSlide].bannerSubheading}</p>
      </div>
      <div className="absolute bottom-[15px] left-1/2 transform -translate-x-1/2 flex border border-white p-[6px] rounded-[40px] box-border z-50">
        {carouselData.map((_, index) => (
          <span
            key={index}
            className={`w-2 h-2 bg-white/30 rounded-full transition-all duration-[0.9s] mx-[5px] cursor-pointer ${currentSlide === index ? "w-10 h-2 bg-white rounded-[24px]" : ""}`}
            onClick={() => setCurrentSlide(index)}
          />
        ))}
      </div>
    </div>
  );
};

export default CarouselEnterEmail;
