"use client";
import FormCtaButton from "@/components/FormCtaButton";
import useStripeCheckout from "@/hooks/useStripeCheckout";
import apiClient from "@/utils/axiosUtil";
import { getLocale, sortPlansByFrequency, transformTextWithBreak } from "@/utils/helperFunctions";
import { trackWebEngageEvent } from "@/utils/webengage";
import { useLocale, useTranslations } from "next-intl";
import Image from "next/image";
import { useEffect, useState } from "react";

export default function Component() {
  const [plans, setPlans] = useState([]);
  const [loading, setLoading] = useState(true);
  const { handleCheckout } = useStripeCheckout();
  const locale = useLocale();
  const lang = getLocale(locale);
  const t = useTranslations("Purchase");

  useEffect(() => {
    trackWebEngageEvent("WebSubscriptionScrReached");
  }, []);

  useEffect(() => {
    const fetchPlans = async () => {
      try {
        const url = `${process.env.NEXT_PUBLIC_SUBSSERVICE_BASE_URL}/plan?version=5.0&l=${lang}&planType=acquisition`;
        const response = await apiClient.get(url);
        const data = response.data;

        // Automatically select the top card by default (highest ID)
        const sortedPlans = sortPlansByFrequency([...data]);
        const updatedPlans = sortedPlans.map((plan, index) => ({
          ...plan,
          isSelected: index === 0, // Select the first plan in the sorted array
        }));

        setPlans(updatedPlans);
        setLoading(false);
      } catch (error) {
        console.error("Failed to fetch plans:", error);
        setLoading(false);
      }
    };
    fetchPlans();
  }, [locale]);

  const handleChangePlan = (plan) => {
    setPlans((prevPlans) =>
      prevPlans.map((p) => ({
        ...p,
        isSelected: p.ID === plan.ID,
      }))
    );
  };

  const handleActivatePlan = () => {
    const selectedPlan = plans.find((plan) => plan.isSelected);
    const planID = selectedPlan.PlanID;
    const authToken = localStorage.getItem("auth_token");
    trackWebEngageEvent("WebSubscriptionNextBtnClk", {
      plan_name: selectedPlan?.PlanName,
      product_id: selectedPlan?.PlanID,
      session_id: authToken,
    });
    handleCheckout({ lang, planID, authToken });
  };

  return (
    <div className="min-h-screen bg-white text-sm md:text-xs">
      <div className="relative w-full h-[15.625rem] overflow-hidden">
        <img className="w-full h-full object-cover" src="/images/saas/banner.svg" alt="Banner" />
        <div className="absolute -top-24 left-0 right-0 bottom-0 flex flex-col justify-center items-center">
          <h1 className="text-[2.5rem] text-[#7c4dff] mb-4 font-bold text-center">{t("Heading")}</h1>
          <div className="flex flex-col gap-4" style={{ placeItems: "flex-start" }}>
            <div className="flex items-center gap-2 justify-center text-[#666] text-[1.1rem]">
              <Image src="/images/saas/crown-icon.svg" alt="Crown" width={24} height={24} />
              <span>{t("SubHeading1")}</span>
            </div>
            <div className="flex items-center gap-2 justify-center text-[#666] text-[1.1rem]">
              <Image src="/images/saas/crown-icon.svg" alt="Crown" width={24} height={24} />
              <span>{t("SubHeading2")}</span>
            </div>
          </div>
        </div>
      </div>

      <main className="max-w-[33.125rem] -mt-8 mx-auto text-center px-4 mb-8">
        <div className="p-8">
          {loading ? (
            <p>Loading plans...</p>
          ) : (
            plans.map((plan) => (
              <div
                onClick={() => handleChangePlan(plan)}
                key={plan.ID}
                className={`${
                  plan.BestValue
                    ? `border-[0.5rem] border-[#eaeaea] cursor-pointer relative`
                    : "border-[0.5rem] border-[#eaeaea]"
                } ${plan.isSelected ? "border-[0.5rem] border-[#ffdf00] cursor-pointer" : ""} rounded-[0.938rem] p-6 mb-6 cursor-pointer`}
              >
                {plan.BestValue !== 0 && (
                  <div className="absolute -top-6 right-2 flex items-center justify-center z-10">
                    <Image
                      src="/images/saas/best-value.svg"
                      alt="Best Value"
                      width={119}
                      height={38}
                    />
                    <span className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-[0.97rem] text-white font-bold whitespace-nowrap pointer-events-none">Best Value</span>
                  </div>
                )}
                <div className={`${plan.isSelected ? "bg-[#ffdf00]" : "bg-[#eaeaea]"} -mx-6 -mt-6 mb-4 px-4 py-4 rounded-t-[0.813rem] flex justify-between items-center`}>
                  <span className="text-[1.13rem]">{plan.PlanTextTransformed.plan}</span>
                  {!plan.BestValue && plan?.PlanTextTransformed?.cpmText && (
                    <span className="bg-[#5a54af] text-white py-2 px-5 rounded-[0.938rem] text-[0.875rem]">{plan.PlanTextTransformed.cpmText}</span>
                  )}
                </div>
                <div className="flex flex-col justify-center items-center">
                  {plan.PlanTextTransformed.nonDiscountedPrice && (
                    <span
                      className="line-through text-[#1a1713] text-[1.45rem] mr-2"
                      style={{ textDecoration: "line-through" }}
                    >
                      {plan.PlanTextTransformed.nonDiscountedPrice}
                    </span>
                  )}
                  <span className="text-[2.91rem] font-bold text-[#1a1713]">{plan.PlanTextTransformed.price}</span>
                  {plan.Price && plan?.Price?.trim()?.length > 0 && (
                    <div className="bg-[#306f5b] text-white inline-block py-2 px-4 rounded-[1.25rem] my-2 text-[1.29rem]">{plan.Price}</div>
                  )}
                </div>
                <p
                  className="text-[#6c757d] font-poppins text-[1.13rem] font-normal text-center m-0"
                  style={{
                    color: plan.BestValue ? "#000" : "",
                  }}
                >
                  {/* {plan.PlanTextTransformed.annualText} {plan.PlanTextTransformed.price} */}
                  {transformTextWithBreak(
                    plan.PlanTextTransformed.annualText,
                    plan.PlanTextTransformed.price,
                    "free"
                  )}
                </p>
              </div>
            ))
          )}

          <FormCtaButton
            onClick={handleActivatePlan}
            text={t("CtaBtn")}
            customStyles="text-[1.5rem] py-3 px-8 w-full"
          />

          {/* <div className="mt-8 mb-0 flex gap-6 justify-center flex-wrap">
                        <Link href="/terms">Terms</Link>
                        <Link href="/privacy-policy">Privacy Policy</Link>
                    </div> */}
        </div>
      </main>
    </div>
  );
}
