const SectionHeading = ({ 
  title, 
  subtitle, 
  level = "h2",
  className = "",
  titleClassName = "",
  subtitleClassName = "",
  centered = true
}) => {
  const HeadingTag = level;
  const headingClass = level === "h1" ? "heading-primary" : 
                      level === "h2" ? "heading-secondary" : 
                      level === "h3" ? "heading-tertiary" : 
                      "heading-quaternary";

  const containerClass = centered ? "section-heading" : "";

  return (
    <div className={`${containerClass} ${className}`}>
      <HeadingTag className={`${headingClass} ${titleClassName}`}>
        {title}
      </HeadingTag>
      {subtitle && (
        <p className={`text-subheading mt-4 ${subtitleClassName}`}>
          {subtitle}
        </p>
      )}
    </div>
  );
};

export default SectionHeading;
