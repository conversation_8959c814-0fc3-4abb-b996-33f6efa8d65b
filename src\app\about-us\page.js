"use client";
import useScrollAnimation from "@/hooks/useScrollAnimation";
import { useTranslations } from "next-intl";
import Image from "next/image";
import { useEffect } from "react";
import Colorine from "../../../public/images/aboutUs/fiks.webp";
import Layout from "../../../public/images/aboutUs/History.webp";
import <PERSON>yo from "../../../public/images/aboutUs/moyo.webp";
import Neta from "../../../public/images/aboutUs/neta.webp";

const AboutPage = () => {
  const t = useTranslations("AboutPage");

  const setRef = useScrollAnimation(
    {
      duration: 1,
      ease: "power3.out",
      start: "top bottom",
    },
    []
  );

  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  return (
    <>
      <title>{t("meta.title")}</title>
      <meta name="description" content={t("meta.description")} />
      <div className="container-main">
        <div className="w-full h-auto bg-[url('/images/aboutUs/banner4k.png')] bg-cover bg-center section-padding overflow-x-hidden">
          <div
            className="flex flex-col justify-center items-center h-full my-[10px] box-border"
            ref={(el) => setRef(el)}
          >
            <div className="mt-[10px] w-full max-w-[1059px] opacity-100 heading-primary text-center text-black box-border">
              {t("mission.title.our")}{" "}
              <span className="text-responsive-xl bg-[#fa7e47] rounded-[100px] py-2 px-5 inline-flex items-center font-nevermind-bold">
                {t("mission.title.highlight")}{" "}
                <Image
                  src="/images/aboutUs/Arrow.png"
                  width={46}
                  height={46}
                  className="align-middle w-[35px] h-[35px] md:w-[46px] md:h-[46px]"
                  alt={t("mission.arrowAlt")}
                />
              </span>
            </div>
            <p className="w-full max-w-[1059px] h-auto text-subheading text-center mt-5 box-border md:mx-10 lg:mx-15">
              {t("mission.description")}
            </p>
          </div>
        </div>

        <div className="bg-[url('/images/aboutUs/bg4.png')] bg-cover bg-center bg-no-repeat">
          <div
            className="text-black text-center w-full gap-0 opacity-100 h-[710px]"
            ref={(el) => setRef(el)}
          >
            <div className="z-[2] inline-block align-middle">
              <h1
                className="inline-block align-middle heading-secondary font-black leading-[89.51px] text-left w-[290px] h-[90px]"
                style={{ position: "sticky" }}
              >
                {t("history.title")}
              </h1>
            </div>
            <div className="max-w-full overflow-auto scrollbar-none">
              <Image
                src={Layout}
                alt={t("history.imageAlt")}
                className="inline-block align-middle ml-[0.2rem] -mt-5 h-[588px] min-w-[2000px]"
              />
            </div>
          </div>
        </div>

        <div className="heading-primary text-center mt-10 mb-5" ref={(el) => setRef(el)}>
          {t("gang.title")}
        </div>

        <div
          className="flex justify-evenly flex-row items-center max-[480px]:flex-col max-[480px]:p-[10px]"
          style={{ background: "#D2991F" }}
        >
          <div className="flex-1 text-center">
            <Image src={Moyo} alt={t("gang.moyo.name")} className="w-full h-full" />
          </div>
          <div className="text-left text-white px-20 py-0 flex-1 font-medium text-left max-[480px]:w-[330px] max-[480px]:h-[229px] max-[480px]:p-[10px]">
            <h1 className="mb-[10px] heading-secondary text-white leading-9 max-[480px]:flex max-[480px]:flex-col">
              {t("gang.moyo.name")}
            </h1>
            <p className="text-subheading text-white text-left w-full">
              {t("gang.moyo.description")}
            </p>
          </div>
        </div>

        <div
          className="flex justify-evenly flex-row items-center max-[480px]:flex-col max-[480px]:p-[10px]"
          style={{ background: "#19788E" }}
        >
          <div className="text-left text-white px-20 py-0 flex-1 font-medium text-left max-[480px]:w-[330px] max-[480px]:h-[229px] max-[480px]:p-[10px]">
            <h1 className="mb-[10px] heading-secondary text-white leading-9 max-[480px]:flex max-[480px]:flex-col">
              {t("gang.fiks.name")}
            </h1>
            <p className="text-subheading text-white text-left w-full">
              {t("gang.fiks.description")}
            </p>
          </div>
          <div className="flex-1 text-center">
            <Image src={Colorine} alt={t("gang.fiks.name")} className="w-full h-full" />
          </div>
        </div>

        <div
          className="flex justify-evenly flex-row items-center max-[480px]:flex-col max-[480px]:p-[10px]"
          style={{ background: "#EC622B" }}
        >
          <div className="flex-1 text-center">
            <Image src={Neta} alt={t("gang.neta.name")} className="w-full h-full" />
          </div>
          <div className="text-left text-white px-20 py-0 flex-1 font-medium text-left max-[480px]:w-[330px] max-[480px]:h-[229px] max-[480px]:p-[10px]">
            <h1 className="mb-[10px] heading-secondary text-white leading-9 max-[480px]:flex max-[480px]:flex-col">
              {t("gang.neta.name")}
            </h1>
            <p className="text-subheading text-white text-left w-full">
              {t("gang.neta.description")}
            </p>
          </div>
        </div>
      </div>
    </>
  );
};
export default AboutPage;
