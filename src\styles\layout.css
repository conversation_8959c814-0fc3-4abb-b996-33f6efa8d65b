/* Layout Components */

/* Layout Components */
.container-main {
  @apply max-w-container mx-auto;
}

/* Section Components */
.section-padding {
  @apply py-16 px-8 md:py-20 md:px-12;
}

.section-heading {
  @apply text-center mb-8 md:mb-12;
}

.section-container {
  @apply max-w-container mx-auto;
}

/* Grid Components */
.grid-auto-fit {
  @apply grid justify-items-center gap-8;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.grid-cards {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6;
}

/* Card Components */
.card-base {
  @apply bg-white rounded-2xl shadow-card border border-gray-200 p-6;
}

.card-hover {
  @apply card-base transition-all duration-300 hover:shadow-card-hover hover:scale-105;
}

.card-game {
  @apply card-hover text-center;
}

.card-pricing {
  @apply card-hover flex flex-col items-center justify-between min-w-[180px] w-80 h-80
         md:w-64 md:h-72 lg:w-72 lg:h-80;
}

.card-product {
  @apply card-hover max-w-[350px] flex flex-col justify-between;
}
