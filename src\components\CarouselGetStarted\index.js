"use client";
import { useEffect, useState } from "react";
// import styles from "./styles.module.css";

const carouselData = [
  {
    bannerImage: "/images/webGl/getStartedBanner/2.webp",
  },
  {
    bannerImage: "/images/webGl/getStartedBanner/1.webp",
  },

  {
    bannerImage: "/images/webGl/getStartedBanner/3.webp",
  },
];

const CarouselGetStarted = ({ onSlideChange }) => {
  const [currentSlide, setCurrentSlide] = useState(0);
  const [touchStartX, setTouchStartX] = useState(0);
  const [touchEndX, setTouchEndX] = useState(0);
  const [touchStartY, setTouchStartY] = useState(0);
  const [touchEndY, setTouchEndY] = useState(0);

  const handleTouchStart = (e) => {
    setTouchStartX(e.touches[0].clientX);
    setTouchStartY(e.touches[0].clientY);
  };

  const handleTouchMove = (e) => {
    setTouchEndX(e.touches[0].clientX);
    setTouchEndY(e.touches[0].clientY);
  };

  const handleTouchEnd = () => {
    const horizontalSwipeDistance = touchStartX - touchEndX;
    const verticalSwipeDistance = touchStartY - touchEndY;

    if (Math.abs(verticalSwipeDistance) < 50) {
      if (horizontalSwipeDistance > 75) {
        setCurrentSlide((prev) => (prev === carouselData.length - 1 ? 0 : prev + 1));
      } else if (horizontalSwipeDistance < -75) {
        setCurrentSlide((prev) => (prev === 0 ? carouselData.length - 1 : prev - 1));
      }
    }

    setTouchStartX(0);
    setTouchEndX(0);
    setTouchStartY(0);
    setTouchEndY(0);
  };

  useEffect(() => {
    onSlideChange(currentSlide);
  }, [currentSlide, onSlideChange]);

  // Auto slide every 10 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev === carouselData.length - 1 ? 0 : prev + 1));
    }, 5000);
    return () => clearInterval(interval);
  }, []);

  return (
    <div className="relative w-full overflow-hidden">
      <div
        className="flex transition-transform duration-300 ease-in-out w-full"
        style={{ transform: `translateX(-${currentSlide * 100}%)` }}
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
      >
        {carouselData.map((item, index) => (
          <div
            key={index}
            className={`flex-none w-full h-[400px] bg-cover bg-center ${currentSlide === index ? "opacity-100" : ""}`}
            style={{ backgroundImage: `url(${item.bannerImage})` }}
          />
        ))}
      </div>
      <div className="absolute bottom-[15px] left-1/2 transform -translate-x-1/2 flex border border-white p-[6px] rounded-[40px] box-border z-50">
        {carouselData.map((_, index) => (
          <span
            key={index}
            className={`w-2 h-2 bg-white opacity-100 rounded-full transition-all duration-[0.9s] mx-[5px] bg-white/30 cursor-pointer ${currentSlide === index ? "w-10 h-2 bg-white rounded-[24px]" : ""}`}
            onClick={() => setCurrentSlide(index)}
          />
        ))}
      </div>
    </div>
  );
};

export default CarouselGetStarted;
