import Image from "next/image";

const Stepper = ({ currentStep, handleBack, steps = [1, 2, 3, 4, 5] }) => {
  return (
    <div className="flex justify-between items-center p-2">
      <div>
        {currentStep > 1 && (
          <Image
            src="/images/webGl/backArrow.png"
            width={10}
            height={10}
            className="cursor-pointer"
            onClick={handleBack}
            alt="Back Arrow"
          />
        )}
      </div>
      <div className="flex items-center justify-end px-4 py-1">
        {steps.map((step, index) => (
          <div key={index} className="flex items-center">
            <div className={`w-5 h-5 rounded-full flex items-center justify-center text-base text-white transition-colors duration-300 ${
              index + 1 <= currentStep ? "bg-green-500" : "bg-gray-300"
            }`}>
              {index < currentStep && (
                <Image src="/images/webGl/check.png" alt="Check Icon" width={12} height={12} />
              )}
            </div>
            {index < steps.length - 1 && (
              <div className={`h-1 w-8 transition-all duration-300 ${
                index + 1 < currentStep
                  ? "bg-gradient-to-r from-green-500 to-green-200"
                  : "bg-gray-300"
              }`} />
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default Stepper;
