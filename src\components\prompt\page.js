"use client";
import Image from "next/image";
// import styles from "./styles.module.css";

const ScreenRotationOverlay = ({ isOpen = false }) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-90 flex flex-col justify-end z-50 overflow-hidden lg:items-center lg:justify-center">
      <div className="flex flex-col items-center w-full text-center mt-auto lg:max-w-[600px] lg:p-5 lg:rounded-[20px] max-[375px]:pb-5">
        <Image
          src="/images/rotate-prompt/rotating-top.webp"
          alt="Rotate device prompt"
          width={50}
          height={50}
          className="mb-4 w-[50px] h-[50px] max-[375px]:w-[50px] max-[375px]:h-[50px] max-[375px]:mb-4 lg:w-20 lg:h-20 lg:mb-6 max-[500px]:landscape:w-[30px] max-[500px]:landscape:h-[30px]"
        />

        <h1 className="text-white text-[2.5rem] font-bold m-0 mb-6 max-[375px]:text-[2rem] max-[375px]:mb-4 lg:text-[3rem] lg:mb-8 max-[500px]:landscape:text-2xl">Turn your screen!</h1>

        <div className="relative flex flex-col items-center mb-6 w-full max-w-[300px] max-[375px]:max-w-[250px] max-[375px]:mb-4 lg:max-w-[500px] lg:mb-8">
          <Image
            src="/images/rotate-prompt/rotate.webp"
            alt="Rotate"
            width={200}
            height={200}
            className="w-[250px] h-auto max-h-[200px] object-contain animate-spin-slow max-[375px]:w-[200px] max-[375px]:max-h-[150px] lg:w-[400px] lg:max-h-[300px] max-[500px]:landscape:w-[110px] max-[500px]:landscape:h-[110px]"
          />
        </div>

        <Image
          src="/images/rotate-prompt/bottom-img.webp"
          alt="Bottom image"
          width={300}
          height={250}
          className="w-[300px] h-auto max-h-[300px] object-contain max-[375px]:w-[250px] max-[375px]:max-h-[250px] lg:w-[400px] lg:max-h-[400px] max-[500px]:landscape:w-[150px] max-[500px]:landscape:h-[100px]"
        />
      </div>
    </div>
  );
};

export default ScreenRotationOverlay;
