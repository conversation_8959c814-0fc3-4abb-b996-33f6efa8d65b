/* Utility Classes */

/* Common utility classes that are used across the application */

.noLinkStyle {
  @apply no-underline;
}

.noLinkStyle:hover {
  @apply no-underline;
}

/* Scrollbar utilities */
.scrollbar-none {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.scrollbar-none::-webkit-scrollbar {
  display: none;
}

/* Animation utilities */
.animate-fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

.animate-slide-up {
  animation: slideUp 0.3s ease-out;
}

.animate-scale-in {
  animation: scaleIn 0.2s ease-out;
}

/* Custom animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0.95);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

/* Focus utilities */
.focus-ring {
  @apply focus:outline-none focus:ring-2 focus:ring-primary-400 focus:ring-offset-2;
}

/* Aspect ratio utilities */
.aspect-video {
  aspect-ratio: 16 / 9;
}

.aspect-square {
  aspect-ratio: 1 / 1;
}

/* Text utilities */
.text-balance {
  text-wrap: balance;
}

.text-pretty {
  text-wrap: pretty;
}
