/* Page-specific Components */

/* About Cards */
.about-card {
  @apply w-80 text-center font-poppins font-medium text-brand-purple-dark
         md:w-72;
}

.about-card-image {
  @apply md:w-72 md:h-52;
}

.about-card-text {
  @apply text-base font-medium text-gray-700;
}

/* Award Components */
.awards-grid {
  @apply flex justify-center items-center gap-8 flex-wrap max-w-[90%] mx-auto;
}

.award-image {
  @apply w-[150px] h-[150px] object-contain;
}

.awards-grid-home {
  @apply grid grid-cols-2 gap-5 lg:grid-cols-7 lg:items-center;
}

.award-image-home {
  @apply md:w-20 md:h-20;
}

/* Review Components */
.review-card {
  @apply pt-4 pr-0 pb-0 pl-0 rounded-lg bg-brand-bg-light flex flex-col items-center justify-between text-brand-purple-dark;
}

.review-content {
  @apply text-base my-4 mx-8 mb-2;
}

.review-footer {
  @apply bg-white rounded-b-lg border-t border-[#e0e0e0] pt-4 mt-4;
}

.review-author {
  @apply text-sm text-[#666] m-0;
}

/* Article Components */
.article-card {
  @apply flex border-b border-[#898989] py-4 gap-[10px] cursor-pointer flex-col md:flex-row md:gap-[30px] md:py-6;
}

.article-image {
  @apply flex items-center md:w-[35%];
}

.article-content {
  @apply w-full flex flex-col relative md:w-[65%];
}

.article-date {
  @apply text-[#b7b7b7] text-sm my-0 mb-7;
}

.article-title {
  @apply my-0 mb-2 heading-tertiary;
}

.article-excerpt {
  @apply m-0 text-[#747474] text-sm pb-10;
}

.article-read-more {
  @apply text-[#0169dd] text-sm absolute m-0 bottom-0 right-0;
}

/* Product Components */
.skill-tag {
  @apply text-sm bg-[#f7f7f7] py-2 px-4 rounded-[2.5rem] border border-[#e9e9e9];
}

/* Get Started Components */
.get-started-container {
  @apply max-w-[480px] md:w-full mx-auto text-center bg-white py-0 pr-0 pb-2 pl-0 box-border;
}

/* Login Components */
.login-container {
  @apply w-full max-w-[480px] mx-auto py-0 px-[2vw] pb-[6vh] flex flex-col items-center bg-white
         shadow-[0_0.5vmin_2vmin_rgba(0,0,0,0.1)] max-[480px]:rounded-none max-[480px]:h-full;
}

/* User Home Screen Components */
.user-home-screen {
  @apply bg-cover bg-center bg-no-repeat min-h-[90vh] w-full overflow-hidden box-border
         container-main relative transition-all duration-300 ease-in-out;
}

.user-home-bg {
  @apply absolute top-0 left-0 w-full h-full object-cover transition-opacity duration-300 ease-in-out;
}

.user-home-header {
  @apply flex items-center gap-[10px] p-4 px-8 justify-between text-subheading;
}

.user-greeting {
  @apply flex items-center gap-[10px];
}

.user-avatar {
  @apply h-20 w-20 object-cover;
}

.user-greeting-text {
  @apply text-subheading;
}

.theme-toggle {
  @apply cursor-pointer relative w-[115px] h-[65px] overflow-hidden;
}
