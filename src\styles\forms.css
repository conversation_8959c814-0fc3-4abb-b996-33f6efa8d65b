/* Form Components */

/* Form Components - Using Global Font System */
.form-input {
  @apply w-full py-3 px-4 border border-gray-300 rounded-xl font-poppins text-base font-normal
         transition-colors duration-200 focus:outline-none focus:border-primary-400
         placeholder:text-gray-500;
}

.form-input-error {
  @apply form-input border-red-500 text-red-500;
}

.form-label {
  @apply text-left text-gray-600 font-poppins text-base font-medium mb-2;
}

.form-helper-text {
  @apply font-poppins text-sm font-normal text-gray-500 mt-1;
}

.form-error-text {
  @apply font-poppins text-sm font-normal text-red-500 mt-1;
}

.form-input-underline {
  @apply h-[52px] border-none bg-transparent border-b border-black py-0 px-4 text-base text-[#5b5b5b] w-full my-4 mx-0
         focus:outline-none placeholder:text-base placeholder:text-[#5b5b5b] box-border;
}

.form-textarea {
  @apply bg-transparent border border-black py-4 px-4 text-base text-[#5b5b5b] w-full my-8 mx-0 rounded-lg box-border
         focus:outline-none placeholder:text-base placeholder:text-[#5b5b5b];
}

.form-input-login {
  @apply w-full py-[2vh] px-[3vw] border-[0.2vmin] border-solid border-[#e0e0e0] rounded-[2vmin]
         text-base transition-[border-color] duration-200 box-border placeholder:text-[#6c757d];
}

.password-toggle-btn {
  @apply absolute right-[3vw] top-1/2 transform -translate-y-1/2 bg-none border-none cursor-pointer
         py-[1vh] px-0 text-[clamp(1rem,2.5vw,1.25rem)];
}

.form-error-message {
  @apply bg-[#f8d7ce] text-[#dd4a38] my-[1vh] mx-0 rounded-[1vmin] py-[1vh] px-[2vw]
         text-sm flex items-center;
}
