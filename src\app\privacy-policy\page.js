import { useTranslations } from "next-intl";

export const metadata = {
  title: "SKIDOS Privacy Policy",
};

const PrivacyPage = () => {
  const t = useTranslations("PrivacyPage");

  return (
    <>
      <div className="container-main">
        <div className="section-heading">
          <h1 className="heading-primary text-center">{t("title")}</h1>
        </div>

        <div className="legal-content">
          <h2 className="text-subheading">
            <span>Introduction:</span>
          </h2>
          <p className="text-body">
            <span>{t("introduction.paragraph1")}</span>
          </p>
          <p className="text-body font-bold mt-[30px]">
            <span>{t("introduction.paragraph2")}</span>
          </p>

          <h2 className="text-subheading mt-16">
            <span>{t("limitedCollection.heading")}</span>
          </h2>
          <p className="text-body">
            <span>{t("limitedCollection.description")}</span>
          </p>
          <p className="text-body">
            <span>(i) </span>
            <span className="font-bold">
              {t("limitedCollection.optInAccount.title")}:{" "}
            </span>
            {t("limitedCollection.optInAccount.description")}
          </p>

          <p className="text-body">
            <span>(ii) </span>
            <span className="font-bold">
              {t("limitedCollection.nonPersonalInfo.title")}{" "}
            </span>
            {t("limitedCollection.nonPersonalInfo.description")}
          </p>

          <p className="text-body">
            <span>(iii) </span>
            <span className="font-bold">
              {t("limitedCollection.crashReports.title")} –{" "}
            </span>
            {t("limitedCollection.crashReports.description")}
          </p>

          <p className="text-body">
            <span>(iv) </span>
            <span className="font-bold">
              {t("limitedCollection.installs.title")} –{" "}
            </span>
            {t("limitedCollection.installs.description")}
          </p>

          <h3 className="text-tertiary mt-16">
            <span>{t("pushNotifications.title")}</span>
          </h3>
          <p className="text-body">{t("pushNotifications.description")}</p>

          <h3 className="text-tertiary mt-16">
            <span>{t("cookies.title")}</span>
          </h3>
          <p className="text-body">{t("cookies.description")}</p>

          <h2 className="text-subheading">{t("complianceSection.coppaTitle")}</h2>
          <p className="text-body">{t("complianceSection.coppaDescription1")}</p>
          <p className="text-body">{t("complianceSection.coppaDescription2")}</p>

          <h2 className="text-subheading">{t("contact.title")}</h2>
          <p className="text-body">
            If you have any questions or concerns regarding this Privacy Policy, please send us an
            email at, or raise a ticket at {t("contact.supportLink")}
          </p>

          <h3 className="text-tertiary">Mailing address</h3>
          <p className="text-body">
            {t("contact.mailingAddress.company")},<br />
            {t("contact.mailingAddress.street")} <br />
            {t("contact.mailingAddress.city")} <br />
            {t("contact.mailingAddress.cvr")}
          </p>
        </div>
      </div>
    </>
  );
};

export default PrivacyPage;
