import { useTranslations } from "next-intl";

export const metadata = {
  title: "SKIDOS Privacy Policy",
};

const PrivacyPage = () => {
  const t = useTranslations("PrivacyPage");

  return (
    <>
      <div>
        <div>
          <h1 className="text-center font-nevermind-bold">{t("title")}</h1>
        </div>

        <div className="font-poppins mx-5 my-1 leading-[1.38rem] text-[0.9rem]">
          <h2>
            <span>Introduction:</span>
          </h2>
          <p className="text-[17px] font-normal leading-[1.4em] pb-[27px] text-justify text-black bg-transparent whitespace-pre-wrap m-0 p-0 border-0 text-[100%] font-inherit">
            <span>{t("introduction.paragraph1")}</span>
          </p>
          <p className="font-poppins font-bold mt-[30px]">
            <span>{t("introduction.paragraph2")}</span>
          </p>

          <h2 className="mt-16">
            <span>{t("limitedCollection.heading")}</span>
          </h2>
          <p className="text-[17px] font-normal leading-[1.4em] pb-[27px] text-justify text-black bg-transparent whitespace-pre-wrap m-0 p-0 border-0 text-[100%] font-inherit">
            <span>{t("limitedCollection.description")}</span>
          </p>
          <p className="text-[17px] font-normal leading-[1.4em] pb-[27px] text-justify text-black bg-transparent whitespace-pre-wrap m-0 p-0 border-0 text-[100%] font-inherit">
            <span>(i) </span>
            <span className="font-bold">
              {t("limitedCollection.optInAccount.title")}:{" "}
            </span>
            {t("limitedCollection.optInAccount.description")}
          </p>

          <p className="text-[17px] font-normal leading-[1.4em] pb-[27px] text-justify text-black bg-transparent whitespace-pre-wrap m-0 p-0 border-0 text-[100%] font-inherit">
            <span>(ii) </span>
            <span className="font-bold">
              {t("limitedCollection.nonPersonalInfo.title")}{" "}
            </span>
            {t("limitedCollection.nonPersonalInfo.description")}
          </p>

          <p className="text-[17px] font-normal leading-[1.4em] pb-[27px] text-justify text-black bg-transparent whitespace-pre-wrap m-0 p-0 border-0 text-[100%] font-inherit">
            <span>(iii) </span>
            <span className="font-bold">
              {t("limitedCollection.crashReports.title")} –{" "}
            </span>
            {t("limitedCollection.crashReports.description")}
          </p>

          <p className="text-[17px] font-normal leading-[1.4em] pb-[27px] text-justify text-black bg-transparent whitespace-pre-wrap m-0 p-0 border-0 text-[100%] font-inherit">
            <span>(iv) </span>
            <span className="font-bold">
              {t("limitedCollection.installs.title")} –{" "}
            </span>
            {t("limitedCollection.installs.description")}
          </p>

          <h3>
            <span className="mt-16 text-[1.1rem] leading-[2.38rem] font-bold">{t("pushNotifications.title")}</span>
          </h3>
          <p className="text-[17px] font-normal leading-[1.4em] pb-[27px] text-justify text-black bg-transparent whitespace-pre-wrap m-0 p-0 border-0 text-[100%] font-inherit">{t("pushNotifications.description")}</p>

          <p>
            <span className="mt-16 text-[1.1rem] leading-[2.38rem] font-bold">{t("cookies.title")}</span>
          </p>
          <p className="text-[17px] font-normal leading-[1.4em] pb-[27px] text-justify text-black bg-transparent whitespace-pre-wrap m-0 p-0 border-0 text-[100%] font-inherit">{t("cookies.description")}</p>

          <h2>{t("complianceSection.coppaTitle")}</h2>
          <p className="text-[17px] font-normal leading-[1.4em] pb-[27px] text-justify text-black bg-transparent whitespace-pre-wrap m-0 p-0 border-0 text-[100%] font-inherit">{t("complianceSection.coppaDescription1")}</p>
          <p className="text-[17px] font-normal leading-[1.4em] pb-[27px] text-justify text-black bg-transparent whitespace-pre-wrap m-0 p-0 border-0 text-[100%] font-inherit">{t("complianceSection.coppaDescription2")}</p>

          <h2>{t("contact.title")}</h2>
          <p className="text-[17px] font-normal leading-[1.4em] pb-[27px] text-justify text-black bg-transparent whitespace-pre-wrap m-0 p-0 border-0 text-[100%] font-inherit">
            If you have any questions or concerns regarding this Privacy Policy, please send us an
            email at, or raise a ticket at {t("contact.supportLink")}
          </p>

          <h3>Mailing address</h3>
          <p className="text-[17px] font-normal leading-[1.4em] pb-[27px] text-justify text-black bg-transparent whitespace-pre-wrap m-0 p-0 border-0 text-[100%] font-inherit">
            {t("contact.mailingAddress.company")},<br />
            {t("contact.mailingAddress.street")} <br />
            {t("contact.mailingAddress.city")} <br />
            {t("contact.mailingAddress.cvr")}
          </p>
        </div>
      </div>
    </>
  );
};

export default PrivacyPage;
