"use client";
import Accordion from "@/components/Accordian";
import FormCtaButton from "@/components/FormCtaButton";
import { useTranslations } from "next-intl";
import Image from "next/image";
import { useRef, useState } from "react";
import { RiAttachmentLine } from "react-icons/ri";
import Mascots from "../../../public/images/contactUs/contactUsMascots.webp";
// import { LiaExternalLinkSquareAltSolid } from "react-icons/lia";

const Support = () => {
  const t = useTranslations("Support");
  const fileInputRef = useRef(null);
  const [fileName, setFileName] = useState(t("form.attachment"));

  const handleFileChange = (event) => {
    const file = event.target.files[0];
    if (file) {
      setFileName(file.name);
    }
  };

  const handleIconClick = () => {
    fileInputRef.current.click();
  };

  return (
    <div className="container-main">
      <header className="support-header">
        <div className="support-content">
          <h2 className="heading-primary m-0">{t("header.title")}</h2>
          <div className="support-contact-info">
            <p className="support-contact-label">{t("header.addressLabel")}</p>
            <p className="text-body">{t("header.address")}</p>
          </div>
          <div className="support-contact-info">
            <p className="support-contact-label">{t("header.emailLabel")}</p>
            <p className="text-body">{t("header.email")}</p>
          </div>
        </div>
        <div className="support-image">
          <Image alt={t("header.mascotAlt")} src={Mascots} className="max-w-[90%] h-auto object-contain" />
        </div>
      </header>

      <div className="support-faq">
        <h2 className="heading-secondary">{t("faq.title")}</h2>
        <Accordion />
      </div>

      <section className="support-form-section">
        <h1 className="heading-secondary">{t("form.title")}</h1>
        <form className="support-form">
          <div className="flex flex-col justify-between">
            <div className="w-full">
              <input
                placeholder={t("form.emailPlaceholder")}
                type="email"
                id="email"
                name="email"
                className="form-input-underline"
              />
            </div>
          </div>
          <div className="w-full">
            <textarea
              placeholder={t("form.messagePlaceholder")}
              id="message"
              name="message"
              rows="10"
              className="form-textarea"
            ></textarea>
          </div>
          <div className="support-attachment">
            <input
              type="file"
              ref={fileInputRef}
              onChange={handleFileChange}
              className="hidden"
            />
            <RiAttachmentLine className="support-attachment-icon" onClick={handleIconClick} />
            {fileName && <div className="support-attachment-name">{fileName}</div>}
          </div>
          <FormCtaButton
            text={t("form.submitButton")}
            customStyles="support-submit-btn"
          />
        </form>
      </section>

      <section className="support-links">
        <h1 className="heading-secondary mb-8">{t("links.title")}</h1>
        <div className="support-link-item">
          <p className="text-body-lg m-0">{t("links.termsOfService")}</p>
          {/* <LiaExternalLinkSquareAltSolid className="text-[2rem]" /> */}
        </div>
        <div className="support-link-item">
          <p className="text-body-lg m-0">{t("links.termsOfService")}</p>
          {/* <LiaExternalLinkSquareAltSolid className="text-[2rem]" /> */}
        </div>
        <div className="support-link-item">
          <p className="text-body-lg m-0">{t("links.termsOfService")}</p>
          {/* <LiaExternalLinkSquareAltSolid className="text-[2rem]" /> */}
        </div>
      </section>
    </div>
  );
};

export default Support;
