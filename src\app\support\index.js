"use client";
import Accordion from "@/components/Accordian";
import { useTranslations } from "next-intl";
import Image from "next/image";
import { useRef, useState } from "react";
import { RiAttachmentLine } from "react-icons/ri";
import Mascots from "../../../public/images/contactUs/contactUsMascots.webp";
// import { LiaExternalLinkSquareAltSolid } from "react-icons/lia";

const Support = () => {
  const t = useTranslations("Support");
  const fileInputRef = useRef(null);
  const [fileName, setFileName] = useState(t("form.attachment"));

  const handleFileChange = (event) => {
    const file = event.target.files[0];
    if (file) {
      setFileName(file.name);
    }
  };

  const handleIconClick = () => {
    fileInputRef.current.click();
  };

  return (
    <div className="max-w-[1728px] mx-auto">
      <header className="max-w-full bg-[url('/images/contactUs/contactUsBanner.webp')] bg-cover bg-center bg-no-repeat flex object-cover flex-col md:flex-row">
        <div className="flex w-[95%] md:w-1/2 justify-center flex-col mx-auto md:px-12 md:my-auto">
          <h2 className="text-[3rem] m-0">{t("header.title")}</h2>
          <div className="flex flex-col font-poppins">
            <p className="font-bold text-[1.5rem] my-4 mx-0 mb-[-0.8rem]">{t("header.addressLabel")}</p>
            <p>{t("header.address")}</p>
          </div>
          <div className="flex flex-col font-poppins">
            <p className="font-bold text-[1.5rem] my-4 mx-0 mb-[-0.8rem]">{t("header.emailLabel")}</p>
            <p>{t("header.email")}</p>
          </div>
        </div>
        <div className="flex w-[95%] md:w-1/2 justify-center flex-col items-center">
          <Image alt={t("header.mascotAlt")} src={Mascots} className="max-w-[90%] h-auto object-contain" />
        </div>
      </header>

      <div className="m-4 md:m-12">
        <h2 className="text-[2.5rem]">{t("faq.title")}</h2>
        <Accordion />
      </div>

      <section className="bg-[#f9f9f9] p-4 md:p-12 w-full mx-auto box-border">
        <h1 className="text-[2.5rem]">{t("form.title")}</h1>
        <form>
          <div className="flex flex-col justify-between">
            <div className="w-full">
              <input
                placeholder={t("form.emailPlaceholder")}
                type="email"
                id="email"
                name="email"
                className="h-[52px] border-none bg-transparent border-b border-black py-0 px-4 text-[1.3rem] text-[#5b5b5b] w-full my-4 mx-0 focus:outline-none"
              />
            </div>
          </div>
          <div className="w-full">
            <textarea
              placeholder={t("form.messagePlaceholder")}
              id="message"
              name="message"
              rows="10"
              className="bg-transparent border border-black py-4 px-4 text-[1.3rem] text-[#5b5b5b] w-full my-8 mx-0 rounded-lg box-border focus:outline-none"
            ></textarea>
          </div>
          <div className="flex items-center gap-4 my-4">
            <input
              type="file"
              ref={fileInputRef}
              onChange={handleFileChange}
              className="hidden"
            />
            <RiAttachmentLine className="text-[1.5rem] cursor-pointer text-[#9258fe] hover:text-[#7a4bf8]" onClick={handleIconClick} />
            {fileName && <div className="text-[#5b5b5b] text-sm">{fileName}</div>}
          </div>
          <button type="submit" className="mt-8 bg-[#9258fe] text-[1.2rem] text-white rounded-[0.8rem] py-[0.8rem] px-[1.5rem] no-underline border-none shadow-[inset_0px_5px_5px_rgba(203,177,252,0.9),0px_5px_6px_rgba(0,0,0,0.978)] cursor-pointer mr-20">
            {t("form.submitButton")}
          </button>
        </form>
      </section>

      <section className="p-4 md:p-12">
        <h1 className="text-[2.5rem] mb-8">{t("links.title")}</h1>
        <div className="flex items-center justify-between py-4 border-b border-[#e0e0e0] cursor-pointer hover:bg-[#f5f5f5] transition-colors">
          <p className="text-[1.2rem] m-0">{t("links.termsOfService")}</p>
          {/* <LiaExternalLinkSquareAltSolid className="text-[2rem]" /> */}
        </div>
        <div className="flex items-center justify-between py-4 border-b border-[#e0e0e0] cursor-pointer hover:bg-[#f5f5f5] transition-colors">
          <p className="text-[1.2rem] m-0">{t("links.termsOfService")}</p>
          {/* <LiaExternalLinkSquareAltSolid className="text-[2rem]" /> */}
        </div>
        <div className="flex items-center justify-between py-4 border-b border-[#e0e0e0] cursor-pointer hover:bg-[#f5f5f5] transition-colors">
          <p className="text-[1.2rem] m-0">{t("links.termsOfService")}</p>
          {/* <LiaExternalLinkSquareAltSolid className="text-[2rem]" /> */}
        </div>
      </section>
    </div>
  );
};

export default Support;
