import Image from "next/image";

const GameCard = ({ 
  game, 
  onClick, 
  className = "",
  showPlayButton = true,
  playButtonSrc = "/images/webGl/userHomeScreen/playBtnPopup.png"
}) => {
  return (
    <div
      role="button"
      tabIndex={0}
      aria-label={`Play ${game.DisplayName || game.name}`}
      onClick={onClick}
      onKeyDown={(e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          onClick();
        }
      }}
      className={`card-game p-4 bg-white rounded-[32px] relative shadow-[0px_9.01px_0px_0px_#000000] border border-gray-300 cursor-pointer max-[500px]:landscape:p-3 max-[500px]:landscape:rounded-[22px] ${className}`}
    >
      <Image
        src={game.IconURL || game.image}
        width={220}
        height={220}
        className="max-w-none max-[500px]:landscape:w-[150px] max-[500px]:landscape:h-[150px]"
        alt={game.DisplayName || game.name}
      />
      <p className="text-label my-2 mx-0 max-[500px]:landscape:my-1 max-[500px]:landscape:mx-0">
        {game.DisplayName || game.name}
      </p>
      {showPlayButton && (
        <Image
          width={65}
          height={65}
          src={playButtonSrc}
          className="absolute -bottom-[35px] left-20 max-[500px]:landscape:-bottom-[43px] max-[500px]:landscape:left-[70px]"
          alt="Play Button"
        />
      )}
    </div>
  );
};

export default GameCard;
