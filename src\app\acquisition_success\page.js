"use client";
import FormCtaButton from "@/components/FormCtaButton";
import { useAuth } from "@/context/AuthContext";
import useTogglePinkFooter from "@/hooks/useTogglePinkFooter";
import apiClient from "@/utils/axiosUtil";
import { useTranslations } from "next-intl";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

const AcquisitionSuccess = () => {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  useTogglePinkFooter(true);
  const { login } = useAuth();
  const t = useTranslations("AcquisitionSuccess");

  useEffect(() => {
    const navbar = document.getElementById("navbar");
    if (navbar) navbar.style.display = "none";

    return () => {
      if (navbar) navbar.style.display = "flex";
    };
  }, []);

  const checkUserAuthenticated = async () => {
    setLoading(true);
    const token = localStorage.getItem("auth_token");
    if (token) {
      const url = `${process.env.NEXT_PUBLIC_USERSERVICE_BASE_URL}/user?language=en_us&gameid=doctor&version=8.2`;
      try {
        const response = await apiClient.get(`${url}`);
        const data = response.data;
        localStorage.setItem(
          "UserDetails",
          JSON.stringify({
            email: data.Email,
            expiryDate: data.SubscriptionExpire,
            isSubscribed: data.isSubscribed,
            ...data,
          })
        );
        if (data.isSubscribed) {
          const newToken = localStorage.getItem("auth_token");
          document.cookie = `token=${newToken}; Path=/; Secure; SameSite=Strict; Max-Age=${process.env.NEXT_PUBLIC_COOKIE_EXPIRY}`;
          login(data.isSubscribed);
          router.push("/user-home-screen");
        } else {
          router.push("/acquisition");
        }
      } catch (error) {
        console.error("Error fetching home screen data:", error);
      }
    } else {
      router.push("/acquisition");
    }
    setLoading(false);
  };

  return (
    <div className="text-center max-w-[700px] mx-auto text-[1.3rem] py-8 px-4 min-h-screen">
      <p>
        {t("PaymentSuccessful")}{" "}
        <Image src="/images/webGl/successCheck.png" width={26} height={13} alt="Success Check" />
      </p>
      <h1 className="text-[3rem] m-0">{t("WelcomeMessage")}</h1>
      <p className="text-[#777777] text-[1.3rem] font-poppins font-medium">{t("LearnMessage")}</p>
      <div className="bg-[#ebebeb] p-4 rounded-2xl font-poppins border border-[#cccccc]">
        <p className="m-0">{t("LoginInstruction")}</p>
      </div>
      <div className="m-4">
        <Image src="/images/webGl/scanner.png" width={240} height={154} alt="Scanner" />
      </div>
      <FormCtaButton
        loading={loading}
        text={t("GoToHomeButton")}
        onClick={checkUserAuthenticated}
      />
    </div>
  );
};

export default AcquisitionSuccess;
