"use client";
import FormCtaButton from "@/components/FormCtaButton";
import { useAuth } from "@/context/AuthContext";
import useTogglePinkFooter from "@/hooks/useTogglePinkFooter";
import apiClient from "@/utils/axiosUtil";
import { useTranslations } from "next-intl";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

const AcquisitionSuccess = () => {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  useTogglePinkFooter(true);
  const { login } = useAuth();
  const t = useTranslations("AcquisitionSuccess");

  useEffect(() => {
    const navbar = document.getElementById("navbar");
    if (navbar) navbar.style.display = "none";

    return () => {
      if (navbar) navbar.style.display = "flex";
    };
  }, []);

  const checkUserAuthenticated = async () => {
    setLoading(true);
    const token = localStorage.getItem("auth_token");
    if (token) {
      const url = `${process.env.NEXT_PUBLIC_USERSERVICE_BASE_URL}/user?language=en_us&gameid=doctor&version=8.2`;
      try {
        const response = await apiClient.get(`${url}`);
        const data = response.data;
        localStorage.setItem(
          "UserDetails",
          JSON.stringify({
            email: data.Email,
            expiryDate: data.SubscriptionExpire,
            isSubscribed: data.isSubscribed,
            ...data,
          })
        );
        if (data.isSubscribed) {
          const newToken = localStorage.getItem("auth_token");
          document.cookie = `token=${newToken}; Path=/; Secure; SameSite=Strict; Max-Age=${process.env.NEXT_PUBLIC_COOKIE_EXPIRY}`;
          login(data.isSubscribed);
          router.push("/user-home-screen");
        } else {
          router.push("/acquisition");
        }
      } catch (error) {
        console.error("Error fetching home screen data:", error);
      }
    } else {
      router.push("/acquisition");
    }
    setLoading(false);
  };

  return (
    <div className="container-main section-padding text-center min-h-screen">
      <p className="text-body-lg">
        {t("PaymentSuccessful")}{" "}
        <Image src="/images/webGl/successCheck.png" width={26} height={13} alt="Success Check" />
      </p>
      <h1 className="heading-primary m-0">{t("WelcomeMessage")}</h1>
      <p className="text-subheading text-[#777777]">{t("LearnMessage")}</p>
      <div className="card-base bg-[#ebebeb] border border-[#cccccc]">
        <p className="text-body m-0">{t("LoginInstruction")}</p>
      </div>
      <div className="m-4">
        <Image src="/images/webGl/scanner.png" width={240} height={154} alt="Scanner" />
      </div>
      <FormCtaButton
        loading={loading}
        text={t("GoToHomeButton")}
        onClick={checkUserAuthenticated}
      />
    </div>
  );
};

export default AcquisitionSuccess;
