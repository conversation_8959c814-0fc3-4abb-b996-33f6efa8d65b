"use client";

import Loader from "@/components/Loader";
import { faSquareUpRight } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { useTranslations } from "next-intl";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

const BlogsClient = ({ initialPosts }) => {
  const [articleData, setArticleData] = useState(initialPosts);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [isLoading, setLoading] = useState(false);
  const router = useRouter();
  const t = useTranslations("");
  useEffect(() => {
    // Retrieve saved blog data and page from sessionStorage
    const savedData = sessionStorage.getItem("blogData");
    const savedPage = sessionStorage.getItem("currentPage");

    if (savedData && savedPage) {
      setArticleData(JSON.parse(savedData));
      setPage(parseInt(savedPage, 10));
    }

    // Restore scroll position if available
    const savedPosition = sessionStorage.getItem("scrollPosition");
    if (savedPosition) {
      window.scrollTo(0, parseInt(savedPosition, 10));
    }
    return () => {
      // Save current scroll position when unmounting
      sessionStorage.setItem("scrollPosition", window.scrollY);
    };
  }, []);

  const fetchMorePosts = async () => {
    setLoading(true);
    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/wp-json/wp/v2/posts?per_page=10&page=${page + 1}&after=2020-01-01T00:00:00`
      );
      const newPosts = await response.json();
      if (newPosts.length > 0) {
        const updatedData = [...articleData, ...newPosts];
        setArticleData(updatedData);
        setPage((prev) => prev + 1);
        // Save the new data and page to sessionStorage
        sessionStorage.setItem("blogData", JSON.stringify(updatedData));
        sessionStorage.setItem("currentPage", (page + 1).toString());
      } else {
        setHasMore(false);
      }
    } catch (error) {
      console.error("Error fetching more posts:", error);
    }
    setLoading(false);
  };

  const handleNavigation = (slug) => {
    // Save current scroll position and article data
    sessionStorage.setItem("scrollPosition", window.scrollY);
    sessionStorage.setItem("blogData", JSON.stringify(articleData));
    sessionStorage.setItem("currentPage", page.toString());
    router.push(`/blogs/${slug}`);
  };

  const formatDate = (dateStr) => {
    const date = new Date(dateStr);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  return (
    <>
      <div className="max-w-[1728px] mx-auto">
        <header className="text-center mb-12">
          <h1 className="text-[2rem] md:text-[2.5rem]">{t("blog.essential_reads")}</h1>
          <p className="text-[1.5rem] font-poppins px-6 text-center">{t("blog.reading_benefits")}</p>
        </header>
        <section>
          <h2 className="text-[2rem] pl-4 mb-0 text-center md:pl-12 md:text-left">{t("Footer.Blogs")}</h2>
          <div className="py-0 px-4 pb-4 box-border md:py-0 md:px-12 md:pb-12">
            {articleData.map((item) => (
              <div
                key={item.id}
                className="flex border-b border-[#898989] py-4 gap-[10px] font-poppins cursor-pointer flex-col md:flex-row md:gap-[30px] md:py-6"
                onClick={() => handleNavigation(item.slug)}
              >
                <div className="flex items-center md:w-[35%]">
                  <Image
                    alt={item.yoast_head_json?.og_site_name}
                    src={item.yoast_head_json.schema["@graph"][1].url}
                    className="w-full h-full"
                    width={300}
                    height={240}
                  />
                </div>
                <div className="w-full flex flex-col relative md:w-[65%]">
                  <p className="text-[#b7b7b7] text-[0.8rem] my-0 mb-7">{formatDate(item.date_gmt)}</p>
                  <h3 className="my-0 mb-2 text-[1.5rem] leading-8 md:text-[2rem] md:leading-10" dangerouslySetInnerHTML={{ __html: item.title.rendered }} />
                  <p className="m-0 text-[#747474] leading-[1.2rem] text-[0.9rem] pb-10 md:leading-6">{item.yoast_head_json.description}</p>
                  <p className="text-[#0169dd] text-[0.9rem] absolute m-0 bottom-0 right-0">
                    {t("News.read")} <FontAwesomeIcon icon={faSquareUpRight} />
                  </p>
                </div>
              </div>
            ))}
          </div>
          <div className="flex justify-center items-center my-8 mx-0">
            {isLoading ? (
              <Loader />
            ) : (
              hasMore && (
                <button className="py-3 px-6 my-0 mx-auto rounded-lg border border-black text-[1.2rem] cursor-pointer bg-transparent" onClick={fetchMorePosts}>
                  {t("News.loadMoreBtn")}
                </button>
              )
            )}
          </div>
        </section>
      </div>
    </>
  );
};

export default BlogsClient;
