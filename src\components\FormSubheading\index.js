// import styles from "./styles.module.css";
import Link from "next/link";

const FormSubheading = ({ text, link, linkPath }) => {
  return (
    <p className="mt-3 text-[1.7rem] font-poppins font-medium text-gray-400 max-[480px]:text-[1.1rem]">
      {text}{" "}
      <Link href={linkPath}>
        <span className="text-orange-600 underline cursor-pointer">{link}</span>
      </Link>
    </p>
  );
};

export default FormSubheading;
